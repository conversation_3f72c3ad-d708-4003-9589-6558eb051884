import { NextRequest, NextResponse } from 'next/server'
import { checkBuildMode } from '@/lib/build-utils'

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN

// Cache simples em memória
const cache = new Map()
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutos para partidas
const REQUEST_DELAY = 1500 // 1.5 segundos entre requisições

// Rate limiting
let lastRequestTime = 0
let requestCount = 0
const MAX_REQUESTS_PER_MINUTE = 8

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function fetchFootballData(endpoint: string) {
  // Rate limiting
  const now = Date.now()
  if (now - lastRequestTime < 60000) {
    requestCount++
  } else {
    requestCount = 1
    lastRequestTime = now
  }

  if (requestCount > MAX_REQUESTS_PER_MINUTE) {
    console.log(`⏳ Rate limit atingido, aguardando...`)
    await delay(REQUEST_DELAY)
  }

  // Cache check
  const cacheKey = endpoint
  const cached = cache.get(cacheKey)
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    console.log(`📋 Cache hit: ${endpoint}`)
    return cached.data
  }

  try {
    console.log(`🌐 Buscando dados: ${FOOTBALL_API_URL}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    
    // Cache the result
    cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    })

    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verificar se estamos em modo de build
    const buildResponse = checkBuildMode(request, { matches: [] })
    if (buildResponse) {
      return NextResponse.json(buildResponse)
    }

    console.log("🔍 Buscando partidas de todos os campeonatos...")

    // Proteção adicional para build
    let searchParams
    try {
      if (!request.url) {
        console.log("⚠️ URL não disponível durante build")
        return NextResponse.json({ matches: [], message: "No URL during build" })
      }
      searchParams = new URL(request.url).searchParams
    } catch (urlError) {
      console.log("⚠️ Erro ao processar URL durante build, retornando dados vazios")
      return NextResponse.json({ matches: [], message: "URL parsing error during build" })
    }
    const dateFrom = searchParams?.get('dateFrom') || new Date().toISOString().split('T')[0]
    const dateTo = searchParams?.get('dateTo') || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const status = searchParams?.get('status') || 'SCHEDULED,TIMED'
    const limit = parseInt(searchParams?.get('limit') || '999')

    console.log('🌍 Buscando partidas de TODOS os campeonatos disponíveis:', {
      dateFrom,
      dateTo,
      status,
      limit
    })

    // Primeiro, buscar todas as competições disponíveis
    const allCompetitionsData = await fetchFootballData('/competitions')
    const allCompetitions = allCompetitionsData.competitions || []
    
    console.log(`📊 Total de competições encontradas: ${allCompetitions.length}`)

    const allMatches: any[] = []
    const competitionData: any[] = []
    let processedCompetitions = 0

    // Buscar partidas para cada competição (com limite para evitar timeout)
    const maxCompetitions = 50 // Limitar para evitar timeout
    const competitionsToProcess = allCompetitions.slice(0, maxCompetitions)

    for (const competition of competitionsToProcess) {
      try {
        const compCode = competition.code
        
        console.log(`🏆 Processando competição ${processedCompetitions + 1}/${competitionsToProcess.length}: ${competition.name} (${compCode})`)
        
        // Buscar informações da competição
        competitionData.push(competition)

        // Buscar partidas da competição
        const matchesData = await fetchFootballData(
          `/competitions/${compCode}/matches?dateFrom=${dateFrom}&dateTo=${dateTo}&status=${status}`
        )

        const matches = matchesData.matches || []
        
        // Formatar partidas
        const formattedMatches = matches.map((match: any) => ({
          id: match.id,
          competition: {
            id: competition.id,
            name: competition.name,
            code: compCode,
            emblem: competition.emblem,
            area: competition.area
          },
          homeTeam: {
            id: match.homeTeam.id,
            name: match.homeTeam.name,
            shortName: match.homeTeam.shortName,
            tla: match.homeTeam.tla,
            crest: match.homeTeam.crest
          },
          awayTeam: {
            id: match.awayTeam.id,
            name: match.awayTeam.name,
            shortName: match.awayTeam.shortName,
            tla: match.awayTeam.tla,
            crest: match.awayTeam.crest
          },
          utcDate: match.utcDate,
          status: match.status,
          matchday: match.matchday,
          stage: match.stage,
          score: match.score,
          odds: match.odds || null,
          venue: match.venue || null
        }))

        allMatches.push(...formattedMatches)
        
        console.log(`✅ ${matches.length} partidas encontradas para ${competition.name}`)
        
        processedCompetitions++
        
        // Delay entre requisições para respeitar rate limit
        if (processedCompetitions < competitionsToProcess.length) {
          await delay(REQUEST_DELAY)
        }

      } catch (error) {
        console.error(`❌ Erro ao buscar partidas para ${competition.name}:`, error.message)
        // Continuar com próxima competição em caso de erro
        processedCompetitions++
        continue
      }
    }

    // Ordenar partidas por data
    allMatches.sort((a, b) => new Date(a.utcDate).getTime() - new Date(b.utcDate).getTime())

    // Aplicar limite se especificado
    const limitedMatches = limit > 0 ? allMatches.slice(0, limit) : allMatches

    console.log(`🎯 Total de partidas encontradas: ${limitedMatches.length} de ${processedCompetitions} competições processadas`)

    return NextResponse.json({
      success: true,
      partidas: limitedMatches,
      competitions: competitionData,
      total: limitedMatches.length,
      totalCompetitions: processedCompetitions,
      availableCompetitions: allCompetitions.length,
      filters: {
        dateFrom,
        dateTo,
        status,
        limit
      }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar partidas de todos os campeonatos:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar partidas de todos os campeonatos',
      message: error.message,
      partidas: [],
      competitions: [],
      total: 0
    }, { status: 500 })
  }
}
