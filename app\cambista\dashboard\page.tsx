"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { DollarSign, TrendingUp, Target, LogOut, Plus, Calendar, User, Home, Eye, Printer } from "lucide-react"
import { toast } from "sonner"

interface Cambista {
  id: number
  nome: string
  email: string
  comissao: number
}

interface Aposta {
  id: number
  cliente: string
  telefone: string
  valor: number
  jogos: string[]
  data: string
  status: "pendente" | "ganha" | "perdida"
}

export default function CambistaDashboard() {
  const [cambista, setCambista] = useState<Cambista | null>(null)
  const [apostas, setApostas] = useState<Aposta[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedBilhete, setSelectedBilhete] = useState<any>(null)
  const [showBilheteDialog, setShowBilheteDialog] = useState(false)

  const loadApostasReais = async () => {
    try {
      const response = await fetch('/api/cambista/apostas')
      if (response.ok) {
        const data = await response.json()
        setApostas(data.apostas || [])
      } else {
        console.error('Erro ao carregar apostas')
        setApostas([])
      }
    } catch (error) {
      console.error('Erro ao carregar apostas:', error)
      setApostas([])
    } finally {
      setLoading(false)
    }
  }
  const router = useRouter()

  useEffect(() => {
    const cambistaData = localStorage.getItem("cambista")
    if (!cambistaData) {
      router.push("/cambista/login")
      return
    }

    const parsedCambista = JSON.parse(cambistaData)
    setCambista(parsedCambista)

    // Carregar bilhetes reais do banco de dados
    loadApostasReais()
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem("cambista")
    router.push("/cambista/login")
  }

  const handleViewBilhete = async (bilheteId: number) => {
    try {
      const response = await fetch(`/api/bilhetes/${bilheteId}`)
      if (response.ok) {
        const bilheteData = await response.json()
        setSelectedBilhete(bilheteData)
        setShowBilheteDialog(true)
      } else {
        toast.error("Erro ao carregar detalhes do bilhete")
      }
    } catch (error) {
      console.error("Erro ao buscar bilhete:", error)
      toast.error("Erro ao carregar bilhete")
    }
  }

  const handlePrintBilhete = (bilhete: any) => {
    const printContent = `
==============================
        BILHETE DE APOSTA
==============================
Código: ${bilhete.codigo || bilhete.id}
Data: ${bilhete.data}
Cliente: ${bilhete.cliente}
Valor: R$ ${bilhete.valor.toFixed(2)}
Status: ${bilhete.status.toUpperCase()}
==============================
APOSTAS:
${bilhete.jogos.join('\n')}
==============================
Cambista: ${cambista?.nome}
Sistema de Apostas
==============================
    `

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Bilhete ${bilhete.codigo || bilhete.id}</title>
            <style>
              body { font-family: monospace; font-size: 12px; margin: 20px; }
              pre { white-space: pre-wrap; }
            </style>
          </head>
          <body>
            <pre>${printContent}</pre>
            <script>window.print(); window.close();</script>
          </body>
        </html>
      `)
      printWindow.document.close()
    }
  }

  if (!cambista) {
    return <div>Carregando...</div>
  }

  const totalVendas = apostas.reduce((acc, aposta) => acc + aposta.valor, 0)
  const hoje = new Date().toLocaleDateString('pt-BR')
  const vendasHoje = apostas
    .filter((aposta) => aposta.data.includes(hoje.split('/')[0] + '/' + hoje.split('/')[1]))
    .reduce((acc, aposta) => acc + aposta.valor, 0)
  const comissaoGanha = totalVendas * (cambista.comissao / 100)
  const apostasGanhas = apostas.filter((aposta) => aposta.status === "ganha").length

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{cambista.nome}</h1>
                <p className="text-sm text-gray-500">Cambista - {cambista.comissao}% comissão</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => router.push('/')} variant="outline">
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Sair
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Vendas Hoje</p>
                    <p className="text-3xl font-bold text-gray-900">
                      R$ {vendasHoje.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Vendas</p>
                    <p className="text-3xl font-bold text-gray-900">
                      R$ {totalVendas.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Comissão Ganha</p>
                    <p className="text-3xl font-bold text-gray-900">
                      R$ {comissaoGanha.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Apostas Ganhas</p>
                    <p className="text-3xl font-bold text-gray-900">{apostasGanhas}</p>
                  </div>
                  <Target className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Ações Rápidas */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Bilhetes do Sistema</CardTitle>
                  <CardDescription>Visualize todos os bilhetes criados no sistema</CardDescription>
                </div>

              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Cliente</TableHead>
                      <TableHead>Telefone</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Jogos</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Comissão</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {apostas.map((aposta) => (
                      <TableRow key={aposta.id}>
                        <TableCell className="font-medium">{aposta.cliente}</TableCell>
                        <TableCell>{aposta.telefone}</TableCell>
                        <TableCell>R$ {aposta.valor.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}</TableCell>
                        <TableCell>
                          <div className="max-w-xs">{aposta.jogos.join(", ")}</div>
                        </TableCell>
                        <TableCell>{aposta.data}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              aposta.status === "ganha"
                                ? "default"
                                : aposta.status === "perdida"
                                  ? "destructive"
                                  : "secondary"
                            }
                            className={
                              aposta.status === "ganha"
                                ? "bg-green-100 text-green-800"
                                : aposta.status === "perdida"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {aposta.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          R${" "}
                          {(aposta.valor * (cambista.comissao / 100)).toLocaleString("pt-BR", {
                            minimumFractionDigits: 2,
                          })}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              onClick={() => handleViewBilhete(aposta.id)}
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              onClick={() => handlePrintBilhete(aposta)}
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0"
                            >
                              <Printer className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modal de Visualização do Bilhete */}
      <Dialog open={showBilheteDialog} onOpenChange={setShowBilheteDialog}>
        <DialogContent className="w-[95vw] max-w-md bg-slate-800 border-slate-700 max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white">Detalhes do Bilhete</DialogTitle>
            <DialogDescription className="text-slate-400">
              Informações completas do bilhete
            </DialogDescription>
          </DialogHeader>
          {selectedBilhete && (
            <div className="space-y-4">
              <div className="bg-slate-700 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-400">Código:</span>
                  <span className="text-white font-mono">{selectedBilhete.codigo}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Data/Hora:</span>
                  <span className="text-white">{selectedBilhete.data}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Cliente:</span>
                  <span className="text-white">{selectedBilhete.cliente}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Valor:</span>
                  <span className="text-white font-bold">
                    R$ {selectedBilhete.valor?.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Status:</span>
                  <Badge
                    variant={
                      selectedBilhete.status === "ganha"
                        ? "default"
                        : selectedBilhete.status === "perdida"
                          ? "destructive"
                          : "secondary"
                    }
                    className={
                      selectedBilhete.status === "ganha"
                        ? "bg-green-100 text-green-800"
                        : selectedBilhete.status === "perdida"
                          ? "bg-red-100 text-red-800"
                          : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {selectedBilhete.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Comissão:</span>
                  <span className="text-green-400 font-bold">
                    R$ {(selectedBilhete.valor * (cambista.comissao / 100)).toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-white font-medium">Apostas:</h4>
                <div className="bg-slate-700 p-3 rounded-lg space-y-1">
                  {selectedBilhete.jogos?.map((jogo: string, index: number) => (
                    <div key={index} className="text-sm text-slate-300 border-b border-slate-600 pb-1 last:border-b-0">
                      {jogo}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => handlePrintBilhete(selectedBilhete)}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Imprimir Bilhete
                </Button>
                <Button
                  onClick={() => setShowBilheteDialog(false)}
                  variant="outline"
                  className="border-slate-600 text-slate-400 hover:bg-slate-700"
                >
                  Fechar
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
