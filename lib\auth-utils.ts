// Utilitários para controle de acesso e autenticação

export interface User {
  id: number
  nome: string
  email: string
  tipo: 'admin' | 'gerente' | 'cambista' | 'usuario'
  status: 'ativo' | 'inativo' | 'bloqueado'
  gerente_id?: number
}

export interface AuthContext {
  user: User
  token?: string
}

// Hierarquia de permissões
export const USER_HIERARCHY = {
  admin: 4,
  gerente: 3,
  cambista: 2,
  usuario: 1
}

// Verificar se um usuário pode acessar determinada área
export function canAccessArea(userType: string, requiredLevel: string): boolean {
  const userLevel = USER_HIERARCHY[userType as keyof typeof USER_HIERARCHY] || 0
  const requiredLevelValue = USER_HIERARCHY[requiredLevel as keyof typeof USER_HIERARCHY] || 0
  
  return userLevel >= requiredLevelValue
}

// Verificar se um usuário pode ver outro usuário
export function canViewUser(viewer: User, target: User): boolean {
  switch (viewer.tipo) {
    case 'admin':
      return true // Admin vê todos
      
    case 'gerente':
      // Gerente vê seus cambistas e usuários comuns
      if (target.tipo === 'admin') return false
      if (target.tipo === 'gerente') return target.id === viewer.id // Só vê a si mesmo
      if (target.tipo === 'cambista') return target.gerente_id === viewer.id
      if (target.tipo === 'usuario') return true // Vê todos os usuários comuns
      return false
      
    case 'cambista':
      // Cambista vê apenas usuários comuns
      return target.tipo === 'usuario'
      
    case 'usuario':
      // Usuário comum só vê a si mesmo
      return target.id === viewer.id
      
    default:
      return false
  }
}

// Verificar se um usuário pode editar outro usuário
export function canEditUser(editor: User, target: User): boolean {
  switch (editor.tipo) {
    case 'admin':
      return true // Admin pode editar todos
      
    case 'gerente':
      // Gerente pode editar seus cambistas e usuários comuns
      if (target.tipo === 'admin' || target.tipo === 'gerente') return false
      if (target.tipo === 'cambista') return target.gerente_id === editor.id
      if (target.tipo === 'usuario') return true
      return false
      
    case 'cambista':
      // Cambista pode editar apenas usuários comuns
      return target.tipo === 'usuario'
      
    default:
      return false
  }
}

// Verificar se um usuário pode criar outro tipo de usuário
export function canCreateUserType(creator: User, targetType: string): boolean {
  switch (creator.tipo) {
    case 'admin':
      return ['admin', 'gerente', 'cambista', 'usuario'].includes(targetType)
      
    case 'gerente':
      return ['cambista', 'usuario'].includes(targetType)
      
    case 'cambista':
      return targetType === 'usuario'
      
    default:
      return false
  }
}

// Verificar se um usuário pode excluir outro usuário
export function canDeleteUser(deleter: User, target: User): boolean {
  switch (deleter.tipo) {
    case 'admin':
      return target.tipo !== 'admin' || target.id !== deleter.id // Admin pode excluir todos exceto a si mesmo
      
    case 'gerente':
      // Gerente pode excluir seus cambistas e usuários comuns
      if (target.tipo === 'admin' || target.tipo === 'gerente') return false
      if (target.tipo === 'cambista') return target.gerente_id === deleter.id
      if (target.tipo === 'usuario') return true
      return false
      
    case 'cambista':
      // Cambista pode excluir apenas usuários comuns
      return target.tipo === 'usuario'
      
    default:
      return false
  }
}

// Obter usuários que um determinado usuário pode ver
export function getUsersFilter(viewer: User): {
  whereClause: string
  params: any[]
} {
  switch (viewer.tipo) {
    case 'admin':
      return {
        whereClause: "1=1", // Sem restrições
        params: []
      }
      
    case 'gerente':
      return {
        whereClause: "(tipo IN ('cambista', 'usuario') AND (gerente_id = ? OR gerente_id IS NULL))",
        params: [viewer.id]
      }
      
    case 'cambista':
      return {
        whereClause: "tipo = 'usuario'",
        params: []
      }
      
    default:
      return {
        whereClause: "id = ?",
        params: [viewer.id]
      }
  }
}

// Validar token de autenticação (placeholder - implementar conforme necessário)
export function validateAuthToken(token: string): User | null {
  // Implementar validação de token JWT ou similar
  // Por enquanto, retorna null
  return null
}

// Obter usuário do localStorage (frontend)
export function getUserFromStorage(userType: 'admin' | 'gerente' | 'cambista'): User | null {
  if (typeof window === 'undefined') return null
  
  try {
    const userData = localStorage.getItem(userType)
    if (!userData) return null
    
    const user = JSON.parse(userData)
    
    // Validar estrutura do usuário
    if (!user.id || !user.tipo || !user.email) return null
    
    return user as User
  } catch (error) {
    console.error('Erro ao obter usuário do localStorage:', error)
    return null
  }
}

// Salvar usuário no localStorage (frontend)
export function saveUserToStorage(user: User, userType: 'admin' | 'gerente' | 'cambista'): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(userType, JSON.stringify(user))
  } catch (error) {
    console.error('Erro ao salvar usuário no localStorage:', error)
  }
}

// Remover usuário do localStorage (logout)
export function removeUserFromStorage(userType: 'admin' | 'gerente' | 'cambista'): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.removeItem(userType)
  } catch (error) {
    console.error('Erro ao remover usuário do localStorage:', error)
  }
}

// Verificar se usuário está autenticado
export function isAuthenticated(userType: 'admin' | 'gerente' | 'cambista'): boolean {
  const user = getUserFromStorage(userType)
  return user !== null && user.status === 'ativo'
}
