"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

import {
  Trophy,
  Users,
  DollarSign,
  Target,
  BarChart3,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  UserCheck,
  Loader2,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock
} from "lucide-react"
import { toast } from "sonner"

interface DashboardStats {
  totalBoloes: number
  totalUsuarios: number
  faturamentoMes: number
  apostasHoje: number
  cambistasAtivos: number
  jogosHoje: number
}

interface RecentActivity {
  id: number
  type: "bet" | "payment" | "win" | "user"
  user: string
  amount?: number
  description: string
  time: string
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalBoloes: 0,
    totalUsuarios: 0,
    faturamentoMes: 0,
    apostasHoje: 0,
    cambistasAtivos: 0,
    jogosHoje: 0,
  })
  const [loading, setLoading] = useState(true)
  const [syncLoading, setSyncLoading] = useState(false)
  const [sessoes, setSessoes] = useState<any[]>([])
  const [loadingSessions, setLoadingSessions] = useState(false)

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const [dashboardResponse, activitiesResponse] = await Promise.all([
        fetch("/api/admin/dashboard"),
        fetch("/api/admin/activities")
      ])

      if (!dashboardResponse.ok) {
        throw new Error(`HTTP error! status: ${dashboardResponse.status}`)
      }

      const dashboardData = await dashboardResponse.json()

      if (dashboardData.success) {
        setStats(dashboardData.stats)
      } else {
        console.error("Erro ao carregar dados do dashboard:", dashboardData.message)
      }

      // Carregar atividades se a API estiver funcionando
      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json()
        if (activitiesData.success) {
          setRecentActivity(activitiesData.activities)
        }
      }
    } catch (error) {
      console.error("Erro ao carregar dados do dashboard:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
    loadSessions()

    // Atualizar sessões a cada 30 segundos
    const interval = setInterval(loadSessions, 30000)
    return () => clearInterval(interval)
  }, [])

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])

  const loadSessions = async () => {
    try {
      setLoadingSessions(true)
      const response = await fetch('/api/admin/sessions')
      if (response.ok) {
        const data = await response.json()
        setSessoes(data.sessoes || [])
      }
    } catch (error) {
      console.error('Erro ao carregar sessões:', error)
    } finally {
      setLoadingSessions(false)
    }
  }

  const formatSessionDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const syncFootballAPI = async () => {
    try {
      setSyncLoading(true)
      toast.info("Atualizando dados locais...")

      // Simular carregamento para dar feedback visual
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Verificar campeonatos disponíveis
      const campeonatosResponse = await fetch('/api/campeonatos?status=ativo&limit=999&area=todos')
      const campeonatosData = await campeonatosResponse.json()

      if (campeonatosData.success && campeonatosData.total > 0) {
        toast.success(`✅ ${campeonatosData.total} campeonatos disponíveis`)

        // Verificar partidas disponíveis
        const partidasResponse = await fetch('/api/football/partidas?competitions=PL,PD,SA,BL1,FL1&limit=50')
        const partidasData = await partidasResponse.json()

        if (partidasData.success && partidasData.total > 0) {
          toast.success(`✅ ${partidasData.total} partidas disponíveis`)
        } else {
          toast.info("ℹ️ Nenhuma partida encontrada no momento")
        }

        toast.success("🎯 Sistema atualizado com sucesso!")
      } else {
        toast.warning("⚠️ Nenhum campeonato ativo encontrado")
      }

    } catch (error) {
      console.error('Erro na atualização:', error)
      toast.error("❌ Erro ao atualizar dados. Tente novamente.")
    } finally {
      setSyncLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "bet":
        return <Target className="h-4 w-4 text-blue-500" />
      case "payment":
        return <DollarSign className="h-4 w-4 text-green-500" />
      case "win":
        return <Trophy className="h-4 w-4 text-yellow-500" />
      case "user":
        return <Users className="h-4 w-4 text-purple-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case "bet":
        return "bg-blue-50 border-blue-200"
      case "payment":
        return "bg-green-50 border-green-200"
      case "win":
        return "bg-yellow-50 border-yellow-200"
      case "user":
        return "bg-purple-50 border-purple-200"
      default:
        return "bg-gray-50 border-gray-200"
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Visão geral do sistema de bolões</p>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Bolões</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalBoloes}</p>
                <div className="flex items-center mt-2">
                  <Trophy className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-blue-600 ml-1">Bolões ativos</span>
                </div>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Trophy className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Usuários Ativos</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalUsuarios.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <ArrowUpRight className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600 ml-1">Usuários ativos</span>
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Faturamento Mensal</p>
                <p className="text-3xl font-bold text-gray-900">{formatCurrency(stats.faturamentoMes)}</p>
                <div className="flex items-center mt-2">
                  <DollarSign className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-orange-600 ml-1">Faturamento mensal</span>
                </div>
              </div>
              <div className="bg-orange-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Apostas Hoje</p>
                <p className="text-3xl font-bold text-gray-900">{stats.apostasHoje}</p>
                <div className="flex items-center mt-2">
                  <Target className="h-4 w-4 text-purple-500" />
                  <span className="text-sm text-purple-600 ml-1">Apostas de hoje</span>
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Cambistas Ativos</p>
                <p className="text-2xl font-bold text-gray-900">{stats.cambistasAtivos}</p>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {stats.cambistasAtivos} online
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Jogos Hoje</p>
                <p className="text-2xl font-bold text-gray-900">{stats.jogosHoje}</p>
              </div>
              <Badge variant="default">{stats.jogosHoje} jogos</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Sistema</p>
                <p className="text-2xl font-bold text-gray-900">Online</p>
              </div>
              <Badge variant="secondary">Funcionando</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cards de Ação e Monitoramento */}
      <div className="space-y-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-base">
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar Dados do Sistema Atualize Somente Se for Necessario Aqui E api Futeboll
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 pb-2">
            <Button
              onClick={syncFootballAPI}
              disabled={syncLoading}
              className="w-full h-10 bg-orange-600 hover:bg-orange-700"
            >
              {syncLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Atualizando...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Atualizar Dados
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Atividades Recentes */}
        <Card className="mt-3">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-base">
              <Activity className="h-4 w-4 mr-2" />
              Atividades Recentes
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 pb-2">
            <div className="space-y-2">
              {recentActivity.length === 0 ? (
                <div className="text-center py-4">
                  <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Nenhuma atividade recente</p>
                </div>
              ) : (
                recentActivity.slice(0, 6).map((activity) => (
                  <div
                    key={activity.id}
                    className={`flex items-center justify-between p-2 rounded-lg border text-xs ${getActivityColor(activity.type)}`}
                  >
                    <div className="flex items-center space-x-2">
                      {getActivityIcon(activity.type)}
                      <div>
                        <p className="font-medium">{activity.description}</p>
                        <p className="text-gray-600">{activity.user}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      {activity.amount && (
                        <p className="font-medium text-green-600">
                          R$ {activity.amount.toFixed(2)}
                        </p>
                      )}
                      <p className="text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Monitor de Sessões */}
        <Card className="mt-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-blue-600" />
                <CardTitle className="text-base">Monitoramento de Sessões</CardTitle>
              </div>
              <Button
                onClick={loadSessions}
                variant="outline"
                size="sm"
                disabled={loadingSessions}
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${loadingSessions ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            </div>

            {/* Estatísticas Compactas */}
            <div className="flex space-x-3 mt-3">
              <div className="flex items-center space-x-1 px-2 py-1 bg-blue-50 rounded text-xs">
                <Users className="h-3 w-3 text-blue-600" />
                <span className="font-medium text-blue-800">Total: {sessoes.length}</span>
              </div>
              <div className="flex items-center space-x-1 px-2 py-1 bg-green-50 rounded text-xs">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="font-medium text-green-800">Online: {sessoes.filter(s => s.isOnline).length}</span>
              </div>
              <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-50 rounded text-xs">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span className="font-medium text-yellow-800">Offline: {sessoes.filter(s => !s.isOnline).length}</span>
              </div>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            {loadingSessions ? (
              <div className="text-center py-4">
                <Loader2 className="h-6 w-6 animate-spin mx-auto text-gray-400" />
                <p className="text-gray-500 mt-1 text-sm">Carregando...</p>
              </div>
            ) : (
              <div className="space-y-2">
                {sessoes.map((sessao) => (
                  <div
                    key={sessao.id}
                    className="flex items-center justify-between p-2 border rounded hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      {/* Status e Tipo */}
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            sessao.isOnline
                              ? 'bg-green-400 animate-pulse'
                              : 'bg-yellow-400'
                          }`}
                        />
                        <span className="text-sm">
                          {sessao.tipo === 'admin' ? '👑' : '👨‍💼'}
                        </span>
                      </div>

                      {/* Informações do Usuário */}
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm truncate">{sessao.nome}</span>
                          <Badge className={`text-xs px-1 py-0 ${
                            sessao.tipo === 'admin' ? 'bg-red-100 text-red-800' :
                            sessao.tipo === 'gerente' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {sessao.tipo === 'admin' ? 'Admin' :
                             sessao.tipo === 'gerente' ? 'Gerente' : 'Cambista'}
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-500 truncate">
                          ID: {sessao.id} • {sessao.email}
                        </div>
                      </div>
                    </div>

                    {/* Tempo de Sessão */}
                    <div className="text-right flex-shrink-0">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-gray-400" />
                        <span className="font-mono text-xs font-medium">
                          {formatSessionDuration(sessao.tempoSessao)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {sessao.isOnline ? 'Online agora' : 'Offline'}
                      </div>
                    </div>
                  </div>
                ))}

                {sessoes.length === 0 && (
                  <div className="text-center py-6">
                    <Users className="h-8 w-8 text-gray-300 mx-auto" />
                    <p className="text-gray-500 mt-1 text-sm">Nenhuma sessão encontrada</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
