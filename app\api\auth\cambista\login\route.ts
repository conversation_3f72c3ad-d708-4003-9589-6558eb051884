import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const { email, senha } = await request.json()

    if (!email || !senha) {
      return NextResponse.json({
        success: false,
        error: 'Email e senha são obrigatórios'
      }, { status: 400 })
    }

    await initializeDatabase()

    console.log('🔐 Tentativa de login do cambista:', email)

    // Buscar cambista no banco
    const cambistas = await executeQuery(`
      SELECT
        id, nome, email, senha_hash, tipo, status,
        porcentagem_comissao as comissao, telefone, endereco
      FROM usuarios
      WHERE email = ? AND tipo = 'cambista'
    `, [email])

    if (!cambistas || cambistas.length === 0) {
      console.log('❌ Cambista não encontrado:', email)
      return NextResponse.json({
        success: false,
        error: 'Credenciais inválidas'
      }, { status: 401 })
    }

    const cambista = cambistas[0] as any

    // Verificar se o cambista está ativo
    if (cambista.status !== 'ativo') {
      console.log('❌ Cambista inativo:', email)
      return NextResponse.json({
        success: false,
        error: 'Conta inativa. Entre em contato com o administrador.'
      }, { status: 401 })
    }

    // Verificar senha
    let senhaValida = false

    if (cambista.senha_hash) {
      // Se a senha está hasheada
      if (cambista.senha_hash.startsWith('$2')) {
        senhaValida = await bcrypt.compare(senha, cambista.senha_hash)
      } else {
        // Senha em texto plano (para compatibilidade)
        senhaValida = senha === cambista.senha_hash
      }
    }

    if (!senhaValida) {
      console.log('❌ Senha inválida para cambista:', email)
      return NextResponse.json({
        success: false,
        error: 'Credenciais inválidas'
      }, { status: 401 })
    }

    console.log('✅ Login do cambista realizado com sucesso:', email)

    // Retornar dados do cambista (sem a senha)
    const cambistaData = {
      id: cambista.id,
      nome: cambista.nome,
      email: cambista.email,
      tipo: cambista.tipo,
      status: cambista.status,
      comissao: parseFloat(cambista.comissao || 10),
      telefone: cambista.telefone,
      endereco: cambista.endereco
    }

    return NextResponse.json({
      success: true,
      message: 'Login realizado com sucesso',
      cambista: cambistaData
    })

  } catch (error) {
    console.error('❌ Erro no login do cambista:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
