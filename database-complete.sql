-- =====================================================
-- SISTEMA BOLÃO - BANCO DE DADOS COMPLETO
-- =====================================================
-- Para importar no phpMyAdmin
-- Versão: 1.0
-- Data: 26/07/2025
-- =====================================================

-- Criar banco de dados (descomente se necessário)
-- CREATE DATABASE IF NOT EXISTS `sistema-bolao-top` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE `sistema-bolao-top`;

-- =====================================================
-- CONFIGURAÇÕES INICIAIS
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- ESTRUTURA DAS TABELAS
-- =====================================================

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS `usuarios` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nome` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) UNIQUE NOT NULL,
    `telefone` VARCHAR(20),
    `endereco` TEXT,
    `cpf_cnpj` VARCHAR(20),
    `senha_hash` VARCHAR(255) NOT NULL,
    `tipo` ENUM('admin', 'usuario', 'cambista', 'gerente', 'supervisor') DEFAULT 'usuario',
    `status` ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'ativo',
    `saldo` DECIMAL(10,2) DEFAULT 0.00,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `ultimo_acesso` TIMESTAMP NULL,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Campos específicos para gerentes
    `comissao` DECIMAL(5,2) DEFAULT 0.00,
    `observacao` TEXT,
    `pode_criar_cambista` BOOLEAN DEFAULT FALSE,
    `pode_colocar_comissao` BOOLEAN DEFAULT FALSE,
    
    -- Campos específicos para cambistas
    `porcentagem_comissao` DECIMAL(5,2) DEFAULT 0.00,
    `gerente_id` INT NULL,
    
    -- Campos para afiliados
    `afiliado_id` INT NULL,
    `codigo_afiliado` VARCHAR(20) UNIQUE NULL,
    `total_indicacoes` INT DEFAULT 0,
    `comissao_total` DECIMAL(10,2) DEFAULT 0.00,
    
    INDEX `idx_email` (`email`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_status` (`status`),
    INDEX `idx_gerente_id` (`gerente_id`),
    INDEX `idx_afiliado_id` (`afiliado_id`),
    INDEX `idx_codigo_afiliado` (`codigo_afiliado`),
    
    FOREIGN KEY (`gerente_id`) REFERENCES `usuarios`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`afiliado_id`) REFERENCES `usuarios`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de transações de saldo
CREATE TABLE IF NOT EXISTS `saldo_transacoes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `usuario_id` INT NOT NULL,
    `tipo` ENUM('deposito', 'compra_bilhete', 'premio', 'estorno', 'bonus', 'comissao') NOT NULL,
    `valor` DECIMAL(10,2) NOT NULL,
    `saldo_anterior` DECIMAL(10,2) NOT NULL,
    `saldo_posterior` DECIMAL(10,2) NOT NULL,
    `descricao` VARCHAR(255) NOT NULL,
    `bilhete_id` INT NULL,
    `transaction_id` VARCHAR(255) NULL,
    `status` ENUM('pendente', 'confirmado', 'cancelado') DEFAULT 'confirmado',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_usuario_id` (`usuario_id`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_transaction_id` (`transaction_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    
    FOREIGN KEY (`usuario_id`) REFERENCES `usuarios`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de configurações do sistema
CREATE TABLE IF NOT EXISTS `configuracoes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `chave` VARCHAR(100) UNIQUE NOT NULL,
    `valor` TEXT NOT NULL,
    `descricao` VARCHAR(255),
    `tipo` ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_chave` (`chave`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de campeonatos
CREATE TABLE IF NOT EXISTS `campeonatos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nome` VARCHAR(255) NOT NULL,
    `descricao` TEXT,
    `pais` VARCHAR(100),
    `temporada` VARCHAR(20),
    `status` ENUM('ativo', 'inativo', 'finalizado') DEFAULT 'ativo',
    `data_inicio` DATE,
    `data_fim` DATE,
    `external_id` VARCHAR(50) UNIQUE,
    `logo_url` VARCHAR(500),
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_status` (`status`),
    INDEX `idx_external_id` (`external_id`),
    INDEX `idx_temporada` (`temporada`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de times
CREATE TABLE IF NOT EXISTS `times` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nome` VARCHAR(255) NOT NULL,
    `nome_curto` VARCHAR(50),
    `sigla` VARCHAR(10),
    `pais` VARCHAR(100),
    `logo_url` VARCHAR(500),
    `external_id` VARCHAR(50) UNIQUE,
    `campeonato_id` INT,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_nome` (`nome`),
    INDEX `idx_external_id` (`external_id`),
    INDEX `idx_campeonato_id` (`campeonato_id`),
    
    FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de jogos/partidas
CREATE TABLE IF NOT EXISTS `jogos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `campeonato_id` INT NOT NULL,
    `time_casa_id` INT NOT NULL,
    `time_fora_id` INT NOT NULL,
    `data_jogo` DATETIME NOT NULL,
    `status` ENUM('agendado', 'ao_vivo', 'finalizado', 'adiado', 'cancelado') DEFAULT 'agendado',
    `placar_casa` INT DEFAULT 0,
    `placar_fora` INT DEFAULT 0,
    `resultado` ENUM('casa', 'empate', 'fora') NULL,
    `external_id` VARCHAR(50) UNIQUE,
    `rodada` VARCHAR(50),
    `estadio` VARCHAR(255),
    `arbitro` VARCHAR(255),
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_campeonato_id` (`campeonato_id`),
    INDEX `idx_time_casa_id` (`time_casa_id`),
    INDEX `idx_time_fora_id` (`time_fora_id`),
    INDEX `idx_data_jogo` (`data_jogo`),
    INDEX `idx_status` (`status`),
    INDEX `idx_external_id` (`external_id`),
    INDEX `idx_resultado` (`resultado`),
    
    FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`time_casa_id`) REFERENCES `times`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`time_fora_id`) REFERENCES `times`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de bolões
CREATE TABLE IF NOT EXISTS `boloes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nome` VARCHAR(255) NOT NULL,
    `descricao` TEXT,
    `valor_aposta` DECIMAL(10,2) NOT NULL DEFAULT 25.00,
    `premio_total` DECIMAL(10,2) DEFAULT 0.00,
    `data_inicio` DATETIME NOT NULL,
    `data_fim` DATETIME NOT NULL,
    `status` ENUM('ativo', 'inativo', 'finalizado', 'cancelado') DEFAULT 'ativo',
    `max_apostas_usuario` INT DEFAULT 50,
    `tipo_bolao` ENUM('simples', 'multiplo', 'especial') DEFAULT 'simples',
    `regras` TEXT,
    `banner_image` VARCHAR(500),
    `criado_por` INT,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_status` (`status`),
    INDEX `idx_data_inicio` (`data_inicio`),
    INDEX `idx_data_fim` (`data_fim`),
    INDEX `idx_criado_por` (`criado_por`),
    INDEX `idx_tipo_bolao` (`tipo_bolao`),
    
    FOREIGN KEY (`criado_por`) REFERENCES `usuarios`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de jogos do bolão
CREATE TABLE IF NOT EXISTS `bolao_jogos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `bolao_id` INT NOT NULL,
    `jogo_id` INT NOT NULL,
    `ordem` INT DEFAULT 1,
    `peso` DECIMAL(3,2) DEFAULT 1.00,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY `unique_bolao_jogo` (`bolao_id`, `jogo_id`),
    INDEX `idx_bolao_id` (`bolao_id`),
    INDEX `idx_jogo_id` (`jogo_id`),
    INDEX `idx_ordem` (`ordem`),
    
    FOREIGN KEY (`bolao_id`) REFERENCES `boloes`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`jogo_id`) REFERENCES `jogos`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de bilhetes
CREATE TABLE IF NOT EXISTS `bilhetes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `codigo` VARCHAR(20) UNIQUE NOT NULL,
    `usuario_id` INT NOT NULL,
    `bolao_id` INT NOT NULL,
    `valor_aposta` DECIMAL(10,2) NOT NULL,
    `valor_premio` DECIMAL(10,2) DEFAULT 0.00,
    `status` ENUM('pendente', 'pago', 'ganhou', 'perdeu', 'cancelado') DEFAULT 'pendente',
    `pago_com_saldo` BOOLEAN DEFAULT FALSE,
    `acertos` INT DEFAULT 0,
    `total_jogos` INT DEFAULT 0,
    `percentual_acerto` DECIMAL(5,2) DEFAULT 0.00,
    `data_pagamento` TIMESTAMP NULL,
    `data_resultado` TIMESTAMP NULL,
    `qr_code_pix` TEXT,
    `transaction_id` VARCHAR(255),
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_codigo` (`codigo`),
    INDEX `idx_usuario_id` (`usuario_id`),
    INDEX `idx_bolao_id` (`bolao_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_transaction_id` (`transaction_id`),
    INDEX `idx_data_cadastro` (`data_cadastro`),
    
    FOREIGN KEY (`usuario_id`) REFERENCES `usuarios`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`bolao_id`) REFERENCES `boloes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de apostas
CREATE TABLE IF NOT EXISTS `apostas` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `bilhete_id` INT NOT NULL,
    `jogo_id` INT NOT NULL,
    `palpite` ENUM('casa', 'empate', 'fora') NOT NULL,
    `acertou` BOOLEAN DEFAULT FALSE,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY `unique_bilhete_jogo` (`bilhete_id`, `jogo_id`),
    INDEX `idx_bilhete_id` (`bilhete_id`),
    INDEX `idx_jogo_id` (`jogo_id`),
    INDEX `idx_palpite` (`palpite`),
    INDEX `idx_acertou` (`acertou`),
    
    FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`jogo_id`) REFERENCES `jogos`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de pagamentos
CREATE TABLE IF NOT EXISTS `pagamentos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `usuario_id` INT NOT NULL,
    `bilhete_id` INT,
    `valor` DECIMAL(10,2) NOT NULL,
    `metodo` ENUM('PIX', 'cartao', 'boleto', 'saldo') NOT NULL,
    `status` ENUM('pendente', 'aprovado', 'rejeitado', 'cancelado') DEFAULT 'pendente',
    `transaction_id` VARCHAR(255),
    `gateway_response` JSON,
    `data_vencimento` DATETIME,
    `data_pagamento` TIMESTAMP NULL,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_usuario_id` (`usuario_id`),
    INDEX `idx_bilhete_id` (`bilhete_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_metodo` (`metodo`),
    INDEX `idx_transaction_id` (`transaction_id`),
    INDEX `idx_data_cadastro` (`data_cadastro`),
    
    FOREIGN KEY (`usuario_id`) REFERENCES `usuarios`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de banners
CREATE TABLE IF NOT EXISTS `banners` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `titulo` VARCHAR(255) NOT NULL,
    `subtitulo` VARCHAR(255),
    `imagem_url` VARCHAR(500),
    `link_url` VARCHAR(500),
    `cor_gradiente` VARCHAR(100) DEFAULT 'from-blue-600 to-purple-600',
    `ativo` BOOLEAN DEFAULT TRUE,
    `ordem` INT DEFAULT 1,
    `data_inicio` DATETIME,
    `data_fim` DATETIME,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `data_atualizacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX `idx_ativo` (`ativo`),
    INDEX `idx_ordem` (`ordem`),
    INDEX `idx_data_inicio` (`data_inicio`),
    INDEX `idx_data_fim` (`data_fim`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de sessões de usuário
CREATE TABLE IF NOT EXISTS `user_sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `usuario_id` INT NOT NULL,
    `session_token` VARCHAR(255) UNIQUE NOT NULL,
    `ip_address` VARCHAR(45),
    `user_agent` TEXT,
    `last_activity` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_usuario_id` (`usuario_id`),
    INDEX `idx_session_token` (`session_token`),
    INDEX `idx_last_activity` (`last_activity`),
    INDEX `idx_expires_at` (`expires_at`),

    FOREIGN KEY (`usuario_id`) REFERENCES `usuarios`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de logs do sistema
CREATE TABLE IF NOT EXISTS `logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `usuario_id` INT,
    `acao` VARCHAR(255) NOT NULL,
    `tabela_afetada` VARCHAR(100),
    `registro_id` INT,
    `dados_anteriores` JSON,
    `dados_novos` JSON,
    `ip_address` VARCHAR(45),
    `user_agent` TEXT,
    `data_log` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_usuario_id` (`usuario_id`),
    INDEX `idx_data_log` (`data_log`),
    INDEX `idx_acao` (`acao`),
    INDEX `idx_tabela_afetada` (`tabela_afetada`),

    FOREIGN KEY (`usuario_id`) REFERENCES `usuarios`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de notificações
CREATE TABLE IF NOT EXISTS `notificacoes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `usuario_id` INT NOT NULL,
    `titulo` VARCHAR(255) NOT NULL,
    `mensagem` TEXT NOT NULL,
    `tipo` ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    `lida` BOOLEAN DEFAULT FALSE,
    `data_leitura` TIMESTAMP NULL,
    `data_cadastro` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX `idx_usuario_id` (`usuario_id`),
    INDEX `idx_lida` (`lida`),
    INDEX `idx_tipo` (`tipo`),
    INDEX `idx_data_cadastro` (`data_cadastro`),

    FOREIGN KEY (`usuario_id`) REFERENCES `usuarios`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DADOS INICIAIS
-- =====================================================

-- Configurações iniciais do sistema
INSERT IGNORE INTO `configuracoes` (`chave`, `valor`, `descricao`, `tipo`) VALUES
('sistema_nome', 'Sistema Bolão', 'Nome do sistema', 'string'),
('pix_chave', '', 'Chave PIX para recebimentos', 'string'),
('taxa_sistema', '10', 'Taxa do sistema em porcentagem', 'number'),
('max_apostas_usuario', '50', 'Máximo de apostas por usuário por bolão', 'number'),
('tempo_limite_pagamento', '30', 'Tempo limite para pagamento em minutos', 'number'),
('email_suporte', '', 'Email de suporte', 'string'),
('whatsapp_suporte', '', 'WhatsApp de suporte', 'string'),
('valor_aposta_padrao', '25.00', 'Valor padrão da aposta', 'number'),
('comissao_afiliado', '5.00', 'Comissão do afiliado em porcentagem', 'number'),
('sistema_ativo', 'true', 'Sistema ativo ou em manutenção', 'boolean'),
('mensagem_manutencao', 'Sistema em manutenção. Tente novamente mais tarde.', 'Mensagem exibida durante manutenção', 'string');

-- Campeonatos iniciais
INSERT IGNORE INTO `campeonatos` (`nome`, `descricao`, `pais`, `temporada`, `status`, `external_id`) VALUES
('Campeonato Brasileiro Série A', 'Principal campeonato do futebol brasileiro', 'Brasil', '2024', 'ativo', 'BSA'),
('Copa do Brasil', 'Copa nacional do Brasil', 'Brasil', '2024', 'ativo', 'CDB'),
('Copa Libertadores', 'Principal competição sul-americana', 'América do Sul', '2024', 'ativo', 'CONMEBOL'),
('Premier League', 'Campeonato inglês', 'Inglaterra', '2024-25', 'ativo', 'PL'),
('La Liga', 'Campeonato espanhol', 'Espanha', '2024-25', 'ativo', 'PD'),
('Serie A', 'Campeonato italiano', 'Itália', '2024-25', 'ativo', 'SA'),
('Bundesliga', 'Campeonato alemão', 'Alemanha', '2024-25', 'ativo', 'BL1'),
('Ligue 1', 'Campeonato francês', 'França', '2024-25', 'ativo', 'FL1'),
('Champions League', 'Liga dos Campeões da UEFA', 'Europa', '2024-25', 'ativo', 'CL');

-- Banner padrão
INSERT IGNORE INTO `banners` (`titulo`, `subtitulo`, `imagem_url`, `cor_gradiente`, `ativo`, `ordem`) VALUES
('BOLÃO BRASIL', 'Faça suas apostas e concorra a prêmios incríveis!', '/placeholder.svg?height=200&width=400', 'from-green-600 to-blue-600', TRUE, 1);

-- =====================================================
-- TRIGGERS E PROCEDURES
-- =====================================================

-- Trigger para atualizar saldo após transação
DELIMITER $$

CREATE TRIGGER `atualizar_saldo_usuario`
AFTER INSERT ON `saldo_transacoes`
FOR EACH ROW
BEGIN
    UPDATE `usuarios`
    SET `saldo` = NEW.saldo_posterior
    WHERE `id` = NEW.usuario_id;
END$$

-- Trigger para calcular acertos do bilhete
CREATE TRIGGER `calcular_acertos_bilhete`
AFTER UPDATE ON `apostas`
FOR EACH ROW
BEGIN
    DECLARE total_acertos INT DEFAULT 0;
    DECLARE total_jogos INT DEFAULT 0;
    DECLARE percentual DECIMAL(5,2) DEFAULT 0.00;

    -- Contar acertos e total de jogos
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN acertou = TRUE THEN 1 ELSE 0 END) as acertos
    INTO total_jogos, total_acertos
    FROM `apostas`
    WHERE `bilhete_id` = NEW.bilhete_id;

    -- Calcular percentual
    IF total_jogos > 0 THEN
        SET percentual = (total_acertos / total_jogos) * 100;
    END IF;

    -- Atualizar bilhete
    UPDATE `bilhetes`
    SET
        `acertos` = total_acertos,
        `total_jogos` = total_jogos,
        `percentual_acerto` = percentual
    WHERE `id` = NEW.bilhete_id;
END$$

-- Trigger para log de alterações
CREATE TRIGGER `log_usuario_changes`
AFTER UPDATE ON `usuarios`
FOR EACH ROW
BEGIN
    INSERT INTO `logs` (
        `usuario_id`,
        `acao`,
        `tabela_afetada`,
        `registro_id`,
        `dados_anteriores`,
        `dados_novos`
    ) VALUES (
        NEW.id,
        'UPDATE',
        'usuarios',
        NEW.id,
        JSON_OBJECT(
            'nome', OLD.nome,
            'email', OLD.email,
            'status', OLD.status,
            'saldo', OLD.saldo
        ),
        JSON_OBJECT(
            'nome', NEW.nome,
            'email', NEW.email,
            'status', NEW.status,
            'saldo', NEW.saldo
        )
    );
END$$

DELIMITER ;

-- =====================================================
-- ÍNDICES ADICIONAIS PARA PERFORMANCE
-- =====================================================

-- Índices compostos para consultas frequentes
CREATE INDEX `idx_bilhetes_usuario_status` ON `bilhetes` (`usuario_id`, `status`);
CREATE INDEX `idx_apostas_bilhete_acerto` ON `apostas` (`bilhete_id`, `acertou`);
CREATE INDEX `idx_jogos_data_status` ON `jogos` (`data_jogo`, `status`);
CREATE INDEX `idx_pagamentos_usuario_status` ON `pagamentos` (`usuario_id`, `status`);
CREATE INDEX `idx_transacoes_usuario_tipo` ON `saldo_transacoes` (`usuario_id`, `tipo`);

-- =====================================================
-- VIEWS ÚTEIS
-- =====================================================

-- View para estatísticas de usuários
CREATE OR REPLACE VIEW `view_usuario_stats` AS
SELECT
    u.id,
    u.nome,
    u.email,
    u.tipo,
    u.saldo,
    COUNT(DISTINCT b.id) as total_bilhetes,
    COUNT(DISTINCT CASE WHEN b.status = 'ganhou' THEN b.id END) as bilhetes_ganhos,
    COUNT(DISTINCT CASE WHEN b.status = 'perdeu' THEN b.id END) as bilhetes_perdidos,
    COALESCE(SUM(CASE WHEN b.status = 'ganhou' THEN b.valor_premio ELSE 0 END), 0) as total_premios,
    COALESCE(SUM(CASE WHEN p.status = 'aprovado' THEN p.valor ELSE 0 END), 0) as total_apostado,
    u.data_cadastro
FROM `usuarios` u
LEFT JOIN `bilhetes` b ON u.id = b.usuario_id
LEFT JOIN `pagamentos` p ON u.id = p.usuario_id AND p.bilhete_id IS NOT NULL
GROUP BY u.id;

-- View para ranking de usuários
CREATE OR REPLACE VIEW `view_ranking_usuarios` AS
SELECT
    u.id,
    u.nome,
    COUNT(DISTINCT CASE WHEN b.status = 'ganhou' THEN b.id END) as vitorias,
    COALESCE(SUM(CASE WHEN b.status = 'ganhou' THEN b.valor_premio ELSE 0 END), 0) as total_premios,
    COALESCE(AVG(b.percentual_acerto), 0) as media_acertos
FROM `usuarios` u
LEFT JOIN `bilhetes` b ON u.id = b.usuario_id
WHERE u.tipo = 'usuario'
GROUP BY u.id
ORDER BY vitorias DESC, total_premios DESC, media_acertos DESC;

-- =====================================================
-- FINALIZAÇÃO
-- =====================================================

COMMIT;

-- =====================================================
-- INFORMAÇÕES IMPORTANTES
-- =====================================================

/*
INSTRUÇÕES PARA USO:

1. IMPORTAÇÃO:
   - Abra o phpMyAdmin
   - Selecione ou crie o banco de dados
   - Vá em "Importar"
   - Selecione este arquivo SQL
   - Execute a importação

2. CONFIGURAÇÃO INICIAL:
   - Configure as variáveis de ambiente (.env.local)
   - Ajuste as configurações na tabela 'configuracoes'
   - Crie usuários administrativos

3. USUÁRIOS PADRÃO:
   - Execute o script create-test-admin.js para criar usuários de teste
   - Ou crie manualmente via interface admin

4. MANUTENÇÃO:
   - Execute backups regulares
   - Monitore logs na tabela 'logs'
   - Verifique performance com as views criadas

5. SEGURANÇA:
   - Altere senhas padrão
   - Configure SSL/TLS
   - Monitore acessos suspeitos

ESTRUTURA CRIADA:
- 15 tabelas principais
- 3 triggers automáticos
- 2 views para relatórios
- Índices otimizados
- Dados iniciais configurados

VERSÃO: 1.0
DATA: 26/07/2025
COMPATIBILIDADE: MySQL 5.7+ / MariaDB 10.3+
*/
