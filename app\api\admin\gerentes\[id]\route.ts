import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id
    const body = await request.json()
    const { 
      nome, 
      email, 
      telefone, 
      usuario, 
      comissao, 
      senha, 
      observacao, 
      nivel_acesso, 
      pode_criar_cambista, 
      pode_colocar_comissao 
    } = body

    if (!nome || !email || !nivel_acesso) {
      return NextResponse.json({
        success: false,
        error: "Nome, email e nível de acesso são obrigatórios"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Verificar se o gerente existe
    const existingGerente = await executeQuery(
      "SELECT id FROM usuarios WHERE id = ? AND tipo IN ('gerente', 'supervisor', 'admin')",
      [id]
    )

    if (existingGerente.length === 0) {
      return NextResponse.json({
        success: false,
        error: "Gerente não encontrado"
      }, { status: 404 })
    }

    // Verificar se email já existe em outro usuário
    const existingEmail = await executeQuery(
      "SELECT id FROM usuarios WHERE email = ? AND id != ?",
      [email, id]
    )

    if (existingEmail.length > 0) {
      return NextResponse.json({
        success: false,
        error: "Email já está em uso por outro usuário"
      }, { status: 400 })
    }

    // Preparar query de atualização
    let updateQuery = `
      UPDATE usuarios SET 
        nome = ?, email = ?, telefone = ?, usuario = ?, comissao = ?, 
        observacao = ?, tipo = ?, pode_criar_cambista = ?, pode_colocar_comissao = ?
    `
    let updateParams = [
      nome, 
      email, 
      telefone, 
      usuario || email, 
      comissao || 5, 
      observacao || '', 
      nivel_acesso, 
      pode_criar_cambista ? 1 : 0, 
      pode_colocar_comissao ? 1 : 0
    ]

    // Se senha foi fornecida, incluir na atualização
    if (senha && senha.trim() !== '') {
      const senhaHash = await bcrypt.hash(senha, 10)
      updateQuery += ', senha_hash = ?'
      updateParams.push(senhaHash)
    }

    updateQuery += ' WHERE id = ?'
    updateParams.push(id)

    await executeQuery(updateQuery, updateParams)

    return NextResponse.json({
      success: true,
      message: "Gerente atualizado com sucesso"
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar gerente:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id

    await initializeDatabase()

    // Verificar se o gerente existe
    const existingGerente = await executeQuery(
      "SELECT id FROM usuarios WHERE id = ? AND tipo IN ('gerente', 'supervisor', 'admin')",
      [id]
    )

    if (existingGerente.length === 0) {
      return NextResponse.json({
        success: false,
        error: "Gerente não encontrado"
      }, { status: 404 })
    }

    // Desativar o gerente em vez de deletar
    await executeQuery(
      "UPDATE usuarios SET status = 'inativo' WHERE id = ?",
      [id]
    )

    return NextResponse.json({
      success: true,
      message: "Gerente desativado com sucesso"
    })

  } catch (error) {
    console.error("❌ Erro ao desativar gerente:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}
