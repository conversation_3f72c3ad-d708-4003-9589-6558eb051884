import { initializeDatabase, executeQuery } from '../lib/database-config.js'
import fs from 'fs'
import path from 'path'

async function importDatabase() {
  try {
    console.log('📥 Script de Importação do Banco de Dados')
    console.log('=' .repeat(60))
    
    // Verificar se arquivo de exportação existe
    const exportFile = 'database-export-sistema-bolao-top-2025-07-29.sql'
    
    if (!fs.existsSync(exportFile)) {
      console.error(`❌ Arquivo de exportação não encontrado: ${exportFile}`)
      console.log('💡 Execute primeiro: node scripts/verify-database-structure.js')
      process.exit(1)
    }
    
    console.log(`📄 Arquivo encontrado: ${exportFile}`)
    
    // Ler arquivo SQL
    const sqlContent = fs.readFileSync(exportFile, 'utf8')
    console.log(`📊 Tamanho do arquivo: ${(sqlContent.length / 1024).toFixed(2)} KB`)
    
    // Dividir em comandos SQL
    const sqlCommands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'))
    
    console.log(`🔧 Total de comandos SQL: ${sqlCommands.length}`)
    
    // Conectar ao banco
    await initializeDatabase()
    console.log('✅ Conexão com MySQL estabelecida')
    
    console.log('\n⚠️ ATENÇÃO: Este script irá:')
    console.log('   1. Recriar todas as tabelas (DROP + CREATE)')
    console.log('   2. Manter dados existentes (não executa DROP)')
    console.log('   3. Apenas verificar estrutura')
    
    const readline = await import('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const answer = await new Promise(resolve => {
      rl.question('\n❓ Deseja continuar? (s/N): ', resolve)
    })
    rl.close()
    
    if (answer.toLowerCase() !== 's' && answer.toLowerCase() !== 'sim') {
      console.log('❌ Importação cancelada pelo usuário')
      process.exit(0)
    }
    
    console.log('\n🚀 Iniciando importação...')
    
    let successCount = 0
    let errorCount = 0
    let skippedCount = 0
    
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i]
      
      try {
        // Pular comandos perigosos em modo seguro
        if (command.toUpperCase().includes('DROP TABLE') || 
            command.toUpperCase().includes('DROP DATABASE')) {
          console.log(`⚠️ Pulando comando perigoso: ${command.substring(0, 50)}...`)
          skippedCount++
          continue
        }
        
        // Executar comando
        await executeQuery(command)
        
        if (command.toUpperCase().includes('CREATE TABLE')) {
          const tableName = command.match(/CREATE TABLE `?(\w+)`?/i)?.[1]
          console.log(`✅ Tabela verificada: ${tableName}`)
        }
        
        successCount++
        
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`ℹ️ Tabela já existe (OK)`)
          successCount++
        } else {
          console.log(`❌ Erro no comando: ${command.substring(0, 50)}...`)
          console.log(`   Erro: ${error.message}`)
          errorCount++
        }
      }
    }
    
    console.log('\n' + '='.repeat(60))
    console.log('📊 RELATÓRIO DE IMPORTAÇÃO')
    console.log('='.repeat(60))
    console.log(`✅ Comandos executados com sucesso: ${successCount}`)
    console.log(`❌ Comandos com erro: ${errorCount}`)
    console.log(`⚠️ Comandos pulados (segurança): ${skippedCount}`)
    console.log(`📊 Total processado: ${successCount + errorCount + skippedCount}`)
    
    if (errorCount === 0) {
      console.log('\n🎉 IMPORTAÇÃO CONCLUÍDA COM SUCESSO!')
      console.log('✅ Estrutura do banco verificada e atualizada')
    } else {
      console.log('\n⚠️ IMPORTAÇÃO CONCLUÍDA COM AVISOS')
      console.log(`❌ ${errorCount} comandos falharam`)
    }
    
    // Verificar tabelas após importação
    console.log('\n🔍 Verificando tabelas após importação...')
    const tables = await executeQuery(`
      SELECT TABLE_NAME, TABLE_ROWS 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      ORDER BY TABLE_NAME
    `, [process.env.DB_NAME || 'sistema-bolao-top'])
    
    console.log(`📋 Tabelas encontradas: ${tables.length}`)
    tables.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table.TABLE_NAME} (${table.TABLE_ROWS} registros)`)
    })
    
    console.log('\n✅ Verificação concluída!')
    
  } catch (error) {
    console.error('❌ Erro durante importação:', error)
    process.exit(1)
  }
}

// Função para backup antes da importação
async function createBackup() {
  try {
    console.log('💾 Criando backup antes da importação...')
    
    const backupFile = `backup-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.sql`
    
    // Aqui você pode adicionar lógica de backup usando mysqldump
    console.log(`📄 Backup seria salvo em: ${backupFile}`)
    console.log('💡 Para backup completo, use: mysqldump -u user -p database > backup.sql')
    
  } catch (error) {
    console.error('❌ Erro ao criar backup:', error)
  }
}

// Executar importação
if (process.argv.includes('--backup')) {
  await createBackup()
}

importDatabase()
