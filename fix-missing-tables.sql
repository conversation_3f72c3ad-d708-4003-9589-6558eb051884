-- =====================================================
-- SCRIPT PARA CRIAR TABELAS FALTANTES
-- =====================================================
-- Este script cria as tabelas que estão faltando no banco de dados

USE `sistema-bolao-top`;

-- =====================================================
-- TABELA: times
-- =====================================================
CREATE TABLE IF NOT EXISTS `times` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `nome_curto` varchar(50) DEFAULT NULL,
  `cidade` varchar(100) DEFAULT NULL,
  `estado` varchar(100) DEFAULT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `api_id` varchar(50) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  `image_id` varchar(50) DEFAULT NULL COMMENT 'ID da imagem (ex: 1765 para 1765.png)',
  PRIMARY KEY (`id`),
  KEY `idx_nome` (`nome`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_times_api` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: campeonatos (verificar se existe)
-- =====================================================
CREATE TABLE IF NOT EXISTS `campeonatos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `codigo` varchar(10) DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `temporada` varchar(50) DEFAULT NULL,
  `temporada_atual` varchar(50) DEFAULT NULL,
  `status` enum('ativo','encerrado','pausado') DEFAULT 'ativo',
  `data_inicio` date DEFAULT NULL,
  `data_fim` date DEFAULT NULL,
  `api_id` varchar(50) DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_campeonatos_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: jogos (verificar se existe)
-- =====================================================
CREATE TABLE IF NOT EXISTS `jogos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campeonato_id` int(11) NOT NULL,
  `time_casa_id` int(11) NOT NULL,
  `time_fora_id` int(11) NOT NULL,
  `data_jogo` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `local_jogo` varchar(255) DEFAULT NULL,
  `rodada` int(11) DEFAULT NULL,
  `resultado_casa` int(11) DEFAULT NULL,
  `resultado_fora` int(11) DEFAULT NULL,
  `status` enum('agendado','ao_vivo','finalizado','cancelado') DEFAULT 'agendado',
  `api_id` varchar(50) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `time_casa_id` (`time_casa_id`),
  KEY `time_fora_id` (`time_fora_id`),
  KEY `idx_data_jogo` (`data_jogo`),
  KEY `idx_status` (`status`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_campeonato_id` (`campeonato_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- INSERIR DADOS BÁSICOS DE TESTE
-- =====================================================

-- Inserir alguns campeonatos básicos se não existirem
INSERT IGNORE INTO `campeonatos` (`id`, `nome`, `codigo`, `pais`, `status`) VALUES
(1, 'Campeonato Brasileiro Série A', 'BSA', 'Brasil', 'ativo'),
(2, 'Copa do Brasil', 'CB', 'Brasil', 'ativo'),
(3, 'Libertadores', 'LIB', 'América do Sul', 'ativo'),
(4, 'Premier League', 'PL', 'Inglaterra', 'ativo'),
(5, 'La Liga', 'LL', 'Espanha', 'ativo');

-- Inserir alguns times básicos se não existirem
INSERT IGNORE INTO `times` (`id`, `nome`, `nome_curto`, `pais`) VALUES
(1, 'Flamengo', 'FLA', 'Brasil'),
(2, 'Palmeiras', 'PAL', 'Brasil'),
(3, 'São Paulo', 'SAO', 'Brasil'),
(4, 'Corinthians', 'COR', 'Brasil'),
(5, 'Santos', 'SAN', 'Brasil'),
(6, 'Grêmio', 'GRE', 'Brasil'),
(7, 'Internacional', 'INT', 'Brasil'),
(8, 'Atlético Mineiro', 'ATM', 'Brasil'),
(9, 'Cruzeiro', 'CRU', 'Brasil'),
(10, 'Botafogo', 'BOT', 'Brasil');

-- Verificar se as tabelas foram criadas
SELECT 'Tabelas criadas com sucesso!' as resultado;

-- Mostrar contagem de registros
SELECT 
  (SELECT COUNT(*) FROM campeonatos) as total_campeonatos,
  (SELECT COUNT(*) FROM times) as total_times,
  (SELECT COUNT(*) FROM jogos) as total_jogos;
