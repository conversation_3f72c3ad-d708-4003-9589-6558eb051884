"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Search, Download, UserCheck, UserX, Loader2, Edit, Trash2, Key, DollarSign, Users } from "lucide-react"
import { toast } from "sonner"

interface Afiliado {
  id: number
  nome: string
  email: string
  telefone: string
  codigo_afiliado: string
  percentual_comissao: number
  cpa_valor?: number
  tipo_comissao?: string
  comissao_total: number
  total_indicacoes: number
  status: "ativo" | "inativo" | "bloqueado"
  data_cadastro: string
}

interface Stats {
  total: number
  ativos: number
  inativos: number
  totalComissoes: number
  comissoesHoje: number
}

export default function AfiliadosPage() {
  const [afiliados, setAfiliados] = useState<Afiliado[]>([])
  const [stats, setStats] = useState<Stats>({ total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("todos")
  
  // Estados para modais
  const [showEditModal, setShowEditModal] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [selectedAfiliado, setSelectedAfiliado] = useState<Afiliado | null>(null)

  // Estado para formulário de edição
  const [editFormData, setEditFormData] = useState({
    nome: "",
    email: "",
    telefone: "",
    percentual_comissao: 5,
    cpa_valor: 0,
    tipo_comissao: "percentual"
  })

  // Estado para senha
  const [newPassword, setNewPassword] = useState("")
  


  const fetchData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (statusFilter !== "todos") params.append("status", statusFilter)
      if (searchTerm) params.append("search", searchTerm)

      const response = await fetch(`/api/admin/afiliados?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.error) {
        console.error("API Error:", data.error)
        toast.error("Erro ao carregar afiliados")
      }

      setAfiliados(data.afiliados || [])
      setStats(data.stats || { total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 })
    } catch (error) {
      console.error("Erro ao carregar afiliados:", error)
      toast.error("Erro ao carregar afiliados")
      setAfiliados([])
      setStats({ total: 0, ativos: 0, inativos: 0, totalComissoes: 0, comissoesHoje: 0 })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [statusFilter])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchData()
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])



  const handleEdit = async () => {
    if (!selectedAfiliado) return

    try {
      const response = await fetch(`/api/admin/afiliados/${selectedAfiliado.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(editFormData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Erro ao atualizar afiliado")
      }

      toast.success("Afiliado atualizado com sucesso!")
      setShowEditModal(false)
      setSelectedAfiliado(null)
      setEditFormData({ nome: "", email: "", telefone: "", percentual_comissao: 5, cpa_valor: 0, tipo_comissao: "percentual" })
      fetchData()
    } catch (error: any) {
      toast.error(error.message || "Erro ao atualizar afiliado")
    }
  }

  const handleChangePassword = async () => {
    if (!selectedAfiliado || !newPassword) return

    try {
      const response = await fetch(`/api/admin/afiliados/${selectedAfiliado.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ senha: newPassword })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Erro ao alterar senha")
      }

      toast.success("Senha alterada com sucesso!")
      setShowPasswordModal(false)
      setSelectedAfiliado(null)
      setNewPassword("")
    } catch (error: any) {
      toast.error(error.message || "Erro ao alterar senha")
    }
  }

  const handleDelete = async (afiliado: Afiliado) => {
    if (!confirm(`Tem certeza que deseja excluir o afiliado ${afiliado.nome}?`)) return

    try {
      const response = await fetch(`/api/admin/afiliados/${afiliado.id}`, {
        method: "DELETE"
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Erro ao excluir afiliado")
      }

      toast.success("Afiliado excluído com sucesso!")
      fetchData()
    } catch (error: any) {
      toast.error(error.message || "Erro ao excluir afiliado")
    }
  }

  const openEditModal = (afiliado: Afiliado) => {
    setSelectedAfiliado(afiliado)
    setEditFormData({
      nome: afiliado.nome,
      email: afiliado.email,
      telefone: afiliado.telefone,
      percentual_comissao: afiliado.percentual_comissao,
      cpa_valor: 0,
      tipo_comissao: "percentual"
    })
    setShowEditModal(true)
  }

  const openPasswordModal = (afiliado: Afiliado) => {
    setSelectedAfiliado(afiliado)
    setNewPassword("")
    setShowPasswordModal(true)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ativo":
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>
      case "inativo":
        return <Badge variant="secondary">Inativo</Badge>
      case "bloqueado":
        return <Badge variant="destructive">Bloqueado</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Afiliados</h1>
          <p className="text-gray-600 mt-2">Gerencie todos os afiliados do sistema</p>
        </div>

      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Afiliados</p>
                <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Afiliados Ativos</p>
                <p className="text-3xl font-bold text-green-600">{stats.ativos}</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inativos</p>
                <p className="text-3xl font-bold text-gray-600">{stats.inativos}</p>
              </div>
              <UserX className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Comissões</p>
                <p className="text-3xl font-bold text-green-600">R$ {parseFloat(stats.totalComissoes.toString()).toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Comissões Hoje</p>
                <p className="text-3xl font-bold text-blue-600">R$ {parseFloat(stats.comissoesHoje.toString()).toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Afiliados */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Afiliados</CardTitle>
          <CardDescription>Visualize e gerencie todos os afiliados cadastrados</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nome, email ou código..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos...</SelectItem>
                <SelectItem value="ativo">Ativo</SelectItem>
                <SelectItem value="inativo">Inativo</SelectItem>
                <SelectItem value="bloqueado">Bloqueado</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Afiliado</TableHead>
                  <TableHead>Contato</TableHead>
                  <TableHead>Código</TableHead>
                  <TableHead>Comissão</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Data Cadastro</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {afiliados.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      Nenhum afiliado encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  afiliados.map((afiliado) => (
                    <TableRow key={afiliado.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarImage src={`/placeholder.svg?height=32&width=32`} />
                            <AvatarFallback>
                              {afiliado.nome
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{afiliado.nome}</p>
                            <p className="text-sm text-gray-500">ID: {afiliado.id}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm">{afiliado.email}</p>
                          <p className="text-sm text-gray-500">{afiliado.telefone || "N/A"}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{afiliado.codigo_afiliado}</Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm font-medium">
                            {afiliado.tipo_comissao === "cpa"
                              ? `R$ ${parseFloat(afiliado.cpa_valor?.toString() || "0").toFixed(2)} (CPA)`
                              : `${parseFloat(afiliado.percentual_comissao.toString())}%`
                            }
                          </p>
                          <p className="text-sm text-gray-500">Total: R$ {parseFloat(afiliado.comissao_total.toString()).toFixed(2)}</p>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(afiliado.status)}</TableCell>
                      <TableCell>
                        <p className="text-sm">{new Date(afiliado.data_cadastro).toLocaleDateString("pt-BR")}</p>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditModal(afiliado)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openPasswordModal(afiliado)}
                          >
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(afiliado)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Edição */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Afiliado</DialogTitle>
            <DialogDescription>Altere os dados do afiliado</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-nome">Nome</Label>
              <Input
                id="edit-nome"
                value={editFormData.nome}
                onChange={(e) => setEditFormData({ ...editFormData, nome: e.target.value })}
                placeholder="Nome completo"
              />
            </div>
            <div>
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editFormData.email}
                onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="edit-telefone">Telefone</Label>
              <Input
                id="edit-telefone"
                value={editFormData.telefone}
                onChange={(e) => setEditFormData({ ...editFormData, telefone: e.target.value })}
                placeholder="(11) 99999-9999"
              />
            </div>
            <div>
              <Label htmlFor="edit-percentual">Percentual de Comissão (%)</Label>
              <Input
                id="edit-percentual"
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={editFormData.percentual_comissao}
                onChange={(e) => setEditFormData({ ...editFormData, percentual_comissao: parseFloat(e.target.value) || 0 })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEdit}>Salvar Alterações</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Alteração de Senha */}
      <Dialog open={showPasswordModal} onOpenChange={setShowPasswordModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Alterar Senha</DialogTitle>
            <DialogDescription>
              Digite a nova senha para {selectedAfiliado?.nome}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="nova-senha">Nova Senha</Label>
              <Input
                id="nova-senha"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Digite a nova senha"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPasswordModal(false)}>
              Cancelar
            </Button>
            <Button onClick={handleChangePassword}>Alterar Senha</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
