#!/usr/bin/env node

import dotenv from 'dotenv'

// Carregar variáveis de ambiente
dotenv.config()

const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN
const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'

console.log('🏈 Verificando configuração da Football Data API...')

async function checkFootballAPI() {
  try {
    // Verificar se o token está configurado
    if (!FOOTBALL_API_TOKEN || FOOTBALL_API_TOKEN === 'YOUR_API_KEY_HERE') {
      console.log('❌ Token da API não configurado!')
      console.log('')
      console.log('📋 Para configurar:')
      console.log('1. Acesse: https://www.football-data.org/client/register')
      console.log('2. Registre-se gratuitamente')
      console.log('3. Copie sua API key')
      console.log('4. Edite o arquivo .env e substitua YOUR_API_KEY_HERE pela sua chave')
      console.log('')
      console.log('Exemplo no arquivo .env:')
      console.log('FOOTBALL_API_TOKEN=sua_chave_aqui')
      return false
    }

    console.log('✅ Token configurado')
    console.log(`🔗 URL da API: ${FOOTBALL_API_URL}`)

    // Testar a conexão com a API
    console.log('🌐 Testando conexão com a API...')
    
    const response = await fetch(`${FOOTBALL_API_URL}/competitions`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      console.log(`❌ Erro na API: HTTP ${response.status}`)
      
      if (response.status === 400) {
        console.log('💡 Possíveis causas:')
        console.log('   - Token inválido ou expirado')
        console.log('   - Formato do token incorreto')
        console.log('   - Limite de requisições excedido')
      } else if (response.status === 403) {
        console.log('💡 Token válido mas sem permissão para este endpoint')
        console.log('   - Verifique se sua conta tem acesso aos dados')
      } else if (response.status === 429) {
        console.log('💡 Limite de requisições excedido')
        console.log('   - Aguarde alguns minutos antes de tentar novamente')
      }
      
      return false
    }

    const data = await response.json()
    console.log('✅ Conexão com a API bem-sucedida!')
    console.log(`📊 ${data.competitions?.length || 0} competições disponíveis`)

    // Mostrar algumas competições disponíveis
    if (data.competitions && data.competitions.length > 0) {
      console.log('')
      console.log('🏆 Algumas competições disponíveis:')
      data.competitions.slice(0, 5).forEach(comp => {
        console.log(`   - ${comp.name} (${comp.area?.name || 'N/A'})`)
      })
    }

    return true

  } catch (error) {
    console.error('❌ Erro ao verificar API:', error.message)
    
    if (error.code === 'ENOTFOUND') {
      console.log('💡 Problema de conectividade - verifique sua conexão com a internet')
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 Conexão recusada - verifique se a URL da API está correta')
    }
    
    return false
  }
}

async function showAPIInfo() {
  console.log('')
  console.log('ℹ️ Informações sobre a Football Data API:')
  console.log('')
  console.log('🆓 Plano Gratuito:')
  console.log('   - 10 requisições por minuto')
  console.log('   - Dados de algumas competições')
  console.log('   - Ideal para desenvolvimento e testes')
  console.log('')
  console.log('💰 Planos Pagos:')
  console.log('   - Mais requisições por minuto')
  console.log('   - Acesso a todas as competições')
  console.log('   - Dados em tempo real')
  console.log('')
  console.log('📚 Documentação: https://www.football-data.org/documentation/quickstart')
}

// Executar verificação
if (import.meta.url === `file://${process.argv[1]}`) {
  checkFootballAPI()
    .then((success) => {
      if (!success) {
        showAPIInfo()
        process.exit(1)
      } else {
        console.log('')
        console.log('🎉 Configuração da API está funcionando corretamente!')
        process.exit(0)
      }
    })
    .catch((error) => {
      console.error('❌ Erro na verificação:', error)
      process.exit(1)
    })
}

export { checkFootballAPI }
