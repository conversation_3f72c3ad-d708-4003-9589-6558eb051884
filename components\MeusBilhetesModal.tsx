'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  X,
  Receipt,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Trophy,
  Target,
  Eye
} from 'lucide-react'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Bilhete {
  id: number
  codigo: string
  valor: number
  status: 'pendente' | 'pago' | 'ganhou' | 'perdeu' | 'cancelado'
  bolao: string
  apostas: number | any[]
  data: string
  premio?: number
  dataResultado?: string
  apostasDetalhes?: any[]
}

interface MeusBilhetesModalProps {
  isOpen: boolean
  onClose: () => void
  usuario: Usuario
}

export function MeusBilhetesModal({ isOpen, onClose, usuario }: MeusBilhetesModalProps) {
  const [bilhetes, setBilhetes] = useState<Bilhete[]>([])
  const [loading, setLoading] = useState(false)
  const [filtro, setFiltro] = useState<'todos' | 'pendente' | 'pago' | 'ganhou' | 'perdeu'>('todos')
  const [bilheteSelecionado, setBilheteSelecionado] = useState<Bilhete | null>(null)
  const [mostrandoDetalhes, setMostrandoDetalhes] = useState(false)

  useEffect(() => {
    if (isOpen) {
      carregarBilhetes()
    }
  }, [isOpen])

  const carregarBilhetes = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/user/bilhetes?user_id=${usuario.id}`)
      const data = await response.json()

      if (data.success && data.bilhetes) {
        // Mapear dados da API para o formato esperado
        const bilhetesFormatados: Bilhete[] = data.bilhetes.map((bilhete: any) => {
          // Corrigir formatação da data
          let dataFormatada = 'Data não disponível'
          try {
            if (bilhete.data) {
              // Se já vem formatada da API
              dataFormatada = bilhete.data
            } else if (bilhete.created_at) {
              // Se vem como timestamp
              const date = new Date(bilhete.created_at)
              if (!isNaN(date.getTime())) {
                dataFormatada = date.toLocaleDateString('pt-BR') + ' ' + date.toLocaleTimeString('pt-BR', {
                  hour: '2-digit',
                  minute: '2-digit'
                })
              }
            }
          } catch (error) {
            console.warn('Erro ao formatar data:', error)
          }

          return {
            id: bilhete.db_id || bilhete.id,
            codigo: bilhete.codigo,
            valor: parseFloat(bilhete.valor_total || bilhete.valor || 0),
            status: bilhete.status,
            bolao: bilhete.bolao_nome || 'Bolão',
            apostas: bilhete.quantidade_apostas || bilhete.apostas?.length || 0,
            data: dataFormatada,
            premio: bilhete.premio ? parseFloat(bilhete.premio) : undefined,
            dataResultado: bilhete.data_resultado ? new Date(bilhete.data_resultado).toLocaleString('pt-BR') : undefined,
            apostasDetalhes: bilhete.apostas || []
          }
        })

        setBilhetes(bilhetesFormatados)
      } else {
        console.error('Erro ao carregar bilhetes:', data.error)
        setBilhetes([])
      }
    } catch (error) {
      console.error('Erro ao carregar bilhetes:', error)
      setBilhetes([])
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pago':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pendente':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'ganhou':
        return <Trophy className="h-4 w-4 text-yellow-500" />
      case 'perdeu':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'cancelado':
        return <XCircle className="h-4 w-4 text-gray-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pago: 'bg-green-100 text-green-800',
      pendente: 'bg-yellow-100 text-yellow-800',
      ganhou: 'bg-yellow-100 text-yellow-800',
      perdeu: 'bg-red-100 text-red-800',
      cancelado: 'bg-gray-100 text-gray-800'
    }
    
    const labels = {
      pago: 'Pago',
      pendente: 'Pendente',
      ganhou: 'Ganhou!',
      perdeu: 'Perdeu',
      cancelado: 'Cancelado'
    }

    return (
      <Badge className={`${variants[status as keyof typeof variants]} text-xs`}>
        {labels[status as keyof typeof labels]}
      </Badge>
    )
  }

  const handleVerDetalhes = (bilhete: Bilhete) => {
    setBilheteSelecionado(bilhete)
    setMostrandoDetalhes(true)
  }

  const handleFecharDetalhes = () => {
    setBilheteSelecionado(null)
    setMostrandoDetalhes(false)
  }

  const mostrarBilheteCompleto = (bilhete: Bilhete) => {
    const bilheteFormatado = `
================================
        BOLÃO BRASIL
================================
Bilhete: ${bilhete.codigo}
Data: ${bilhete.data}
Usuário: ${usuario.nome}
Email: ${usuario.email}
--------------------------------
SUAS APOSTAS:
${bilhete.apostasDetalhes && bilhete.apostasDetalhes.length > 0
  ? bilhete.apostasDetalhes.map((aposta: any, index: number) => {
      return `${index + 1}. ${aposta.jogo}
   Resultado: ${aposta.resultado}
--------------------------------`
    }).join('\n')
  : 'Nenhuma aposta encontrada'
}
Valor: R$ ${bilhete.valor.toFixed(2)}
Status: ${bilhete.status.toUpperCase()}
${bilhete.premio ? `Prêmio: R$ ${bilhete.premio.toFixed(2)}` : ''}
--------------------------------

--------------------------------
Boa sorte!
================================`

    // Criar uma nova janela para mostrar o bilhete
    const novaJanela = window.open('', '_blank', 'width=600,height=800,scrollbars=yes')
    if (novaJanela) {
      novaJanela.document.write(`
        <html>
          <head>
            <title>Bilhete - ${bilhete.codigo}</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                white-space: pre-wrap;
                padding: 20px;
                background: #f5f5f5;
              }
              .bilhete {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                max-width: 500px;
                margin: 0 auto;
              }
              .print-btn {
                margin: 20px auto;
                display: block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              }
              @media print {
                .print-btn { display: none; }
                body { background: white; }
                .bilhete { box-shadow: none; }
              }
            </style>
          </head>
          <body>
            <div class="bilhete">${bilheteFormatado.replace(/\n/g, '<br>')}</div>
            <button class="print-btn" onclick="window.print()">Imprimir Bilhete</button>
          </body>
        </html>
      `)
      novaJanela.document.close()
    }
  }

  const bilhetesFiltrados = filtro === 'todos'
    ? bilhetes
    : bilhetes.filter(b => b.status === filtro)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Receipt className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Meus Bilhetes
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Filtros */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFiltro('todos')}
              style={{
                backgroundColor: filtro === 'todos' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'todos' ? '#ffffff' : '#000000',
                border: filtro === 'todos' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'todos') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'todos') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Todos
            </button>
            <button
              onClick={() => setFiltro('pendente')}
              style={{
                backgroundColor: filtro === 'pendente' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'pendente' ? '#ffffff' : '#000000',
                border: filtro === 'pendente' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'pendente') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'pendente') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Pendentes
            </button>
            <button
              onClick={() => setFiltro('pago')}
              style={{
                backgroundColor: filtro === 'pago' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'pago' ? '#ffffff' : '#000000',
                border: filtro === 'pago' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'pago') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'pago') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Pagos
            </button>
            <button
              onClick={() => setFiltro('ganhou')}
              style={{
                backgroundColor: filtro === 'ganhou' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'ganhou' ? '#ffffff' : '#000000',
                border: filtro === 'ganhou' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'ganhou') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'ganhou') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Ganhadores
            </button>
            <button
              onClick={() => setFiltro('perdeu')}
              style={{
                backgroundColor: filtro === 'perdeu' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'perdeu' ? '#ffffff' : '#000000',
                border: filtro === 'perdeu' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'perdeu') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'perdeu') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Perdedores
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-96">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Clock className="h-6 w-6 animate-spin text-blue-600 mr-2" />
              <span className="text-gray-600">Carregando bilhetes...</span>
            </div>
          ) : bilhetesFiltrados.length === 0 ? (
            <div className="text-center py-8">
              <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nenhum bilhete encontrado</p>
            </div>
          ) : (
            <div className="space-y-3">
              {bilhetesFiltrados.map((bilhete) => (
                <div key={bilhete.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Target className="h-5 w-5 text-blue-600 mt-1" />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-medium text-gray-900">
                            {bilhete.codigo}
                          </h3>
                          {getStatusBadge(bilhete.status)}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {bilhete.bolao} • {bilhete.apostas} apostas
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          <span>Criado: {bilhete.data}</span>
                          {bilhete.dataResultado && (
                            <>
                              <span className="text-gray-300">•</span>
                              <span>Resultado: {bilhete.dataResultado}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-1 mb-1">
                        {getStatusIcon(bilhete.status)}
                      </div>
                      <div className="text-sm font-medium text-gray-900 mb-1">
                        Valor: R$ {bilhete.valor.toFixed(2).replace('.', ',')}
                      </div>
                      {bilhete.premio && (
                        <div className="text-sm font-bold text-yellow-600">
                          Prêmio: R$ {bilhete.premio.toFixed(2).replace('.', ',')}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Botão Ver Detalhes */}
                  <div className="mt-3 pt-3 border-t">
                    <div
                      onClick={() => mostrarBilheteCompleto(bilhete)}
                      style={{
                        backgroundColor: '#f8f9fa',
                        color: '#000000',
                        border: '2px solid #000000',
                        padding: '10px 16px',
                        borderRadius: '8px',
                        fontSize: '16px',
                        fontWeight: '700',
                        cursor: 'pointer',
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '10px',
                        transition: 'all 0.2s ease',
                        textAlign: 'center',
                        userSelect: 'none',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#e9ecef'
                        e.currentTarget.style.transform = 'translateY(-1px)'
                        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#f8f9fa'
                        e.currentTarget.style.transform = 'translateY(0)'
                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)'
                      }}
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          mostrarBilheteCompleto(bilhete)
                        }
                      }}
                    >
                      <Eye style={{ width: '18px', height: '18px', color: '#000000' }} />
                      <span style={{ color: '#000000', fontWeight: '700' }}>Ver Detalhes</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div
            onClick={onClose}
            style={{
              backgroundColor: '#4b5563',
              color: '#ffffff',
              border: 'none',
              minHeight: '44px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              width: '100%',
              padding: '12px',
              borderRadius: '6px',
              transition: 'all 0.2s ease',
              outline: 'none',
              boxShadow: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              userSelect: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#374151'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#4b5563'
            }}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onClose()
              }
            }}
          >
            Fechar
          </div>
        </div>
      </div>
    </div>
  )
}
