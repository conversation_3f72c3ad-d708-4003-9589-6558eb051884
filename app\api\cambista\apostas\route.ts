import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    console.log('🎫 Buscando apostas do cambista...')

    // Buscar todos os bilhetes do sistema para o cambista visualizar
    const bilhetes = await executeQuery(`
      SELECT
        b.id,
        b.codigo,
        b.usuario_nome as cliente,
        b.usuario_email as email,
        b.valor_total as valor,
        b.quantidade_apostas,
        b.status,
        b.created_at as data,
        b.updated_at,
        u.nome as usuario_nome,
        u.tipo as usuario_tipo
      FROM bilhetes b
      LEFT JOIN usuarios u ON b.usuario_id = u.id
      ORDER BY b.created_at DESC
      LIMIT 100
    `)

    console.log(`📊 ${bilhetes?.length || 0} bilhetes encontrados`)

    // Formatar bilhetes para o frontend
    const bilhetesFormatados = (bilhetes || []).map((bilhete: any) => {
      const dataFormatada = bilhete.data ? new Date(bilhete.data).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }) : 'Data inválida'

      return {
        id: bilhete.id,
        cliente: bilhete.cliente || 'Cliente não identificado',
        telefone: bilhete.email || '',
        valor: parseFloat(bilhete.valor || 0),
        jogos: [`Bilhete com ${bilhete.quantidade_apostas || 0} apostas`],
        data: dataFormatada,
        status: bilhete.status === 'pago' ? 'ganha' :
                bilhete.status === 'cancelado' ? 'perdida' : 'pendente',
        codigo: bilhete.codigo,
        cambista: bilhete.usuario_nome || 'Sistema',
        comissao: (parseFloat(bilhete.valor || 0) * 0.10).toFixed(2) // 10% de comissão
      }
    })

    return NextResponse.json({
      success: true,
      apostas: bilhetesFormatados,
      total: bilhetesFormatados.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar apostas do cambista:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      apostas: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { cliente, telefone, valor, jogos, cambista_id } = body

    if (!cliente || !valor || !jogos) {
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios não fornecidos'
      }, { status: 400 })
    }

    await initializeDatabase()

    console.log('🎫 Criando nova aposta para cambista...')

    // Gerar código único para o bilhete
    const codigo = `CAM${Date.now().toString().slice(-8)}`

    // Criar bilhete
    const result = await executeQuery(`
      INSERT INTO bilhetes (
        codigo, usuario_id, usuario_nome, usuario_email, 
        valor_total, quantidade_apostas, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'pendente', NOW())
    `, [
      codigo,
      cambista_id || 1,
      cliente,
      telefone,
      parseFloat(valor),
      Array.isArray(jogos) ? jogos.length : 1
    ])

    console.log('✅ Aposta criada com sucesso, ID:', (result as any).insertId)

    return NextResponse.json({
      success: true,
      message: 'Aposta criada com sucesso',
      bilhete_id: (result as any).insertId,
      codigo: codigo
    })

  } catch (error) {
    console.error('❌ Erro ao criar aposta:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
