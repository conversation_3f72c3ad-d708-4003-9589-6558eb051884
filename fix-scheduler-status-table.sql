-- =====================================================
-- CORREÇÃO DA TABELA scheduler_status
-- =====================================================
-- 
-- Este script corrige o erro #1101 - Coluna BLOB 'status' não pode ter um valor padrão (default)
-- O problema ocorre porque colunas TEXT no MySQL não podem ter valores padrão definidos diretamente.
-- 
-- Solução: Alterar o tipo da coluna 'status' de TEXT para VARCHAR(50)
-- 

-- Remover a tabela existente se houver
DROP TABLE IF EXISTS `scheduler_status`;

-- Criar a tabela corrigida
CREATE TABLE `scheduler_status` (
  `id` int(11) NOT NULL,
  `last_run` datetime DEFAULT NULL,
  `next_run` datetime DEFAULT NULL,
  `status` varchar(50) DEFAULT 'idle',
  `error_count` int(11) DEFAULT 0,
  `last_error` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserir registro inicial
INSERT INTO `scheduler_status` (`id`, `status`) VALUES (1, 'idle');

-- Verificar se a tabela foi criada corretamente
SELECT 'Tabela scheduler_status criada com sucesso!' as resultado;
