import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const userType = searchParams.get("user_type") // Tipo do usuário logado
    const userId = searchParams.get("user_id") // ID do usuário logado
    const filters = {
      status: searchParams.get("status") || undefined,
      tipo: searchParams.get("tipo") || undefined,
      search: searchParams.get("search") || undefined,
    }

    console.log(`🔍 Buscando usuários para ${userType} (ID: ${userId})`)

    let usuarios = []
    let whereConditions = []
    let queryParams = []

    // Aplicar filtros baseados no tipo de usuário logado
    switch (userType) {
      case 'admin':
        // Admin vê todos os usuários
        console.log('👑 Admin: Visualizando todos os usuários')
        whereConditions.push("1=1") // Sem restrições
        break

      case 'gerente':
        // Gerente vê apenas seus cambistas e usuários comuns
        console.log('👨‍💼 Gerente: Visualizando cambistas e usuários')
        whereConditions.push("(u.tipo IN ('cambista', 'usuario') AND (u.gerente_id = ? OR u.gerente_id IS NULL))")
        queryParams.push(userId)
        break

      case 'cambista':
        // Cambista vê apenas usuários comuns (não outros cambistas/gerentes)
        console.log('🎯 Cambista: Visualizando apenas usuários comuns')
        whereConditions.push("u.tipo = 'usuario'")
        break

      default:
        // Usuário comum não pode ver outros usuários
        console.log('👤 Usuário comum: Acesso negado')
        return NextResponse.json({
          success: false,
          error: "Acesso negado",
          usuarios: []
        }, { status: 403 })
    }

    // Aplicar filtros adicionais
    if (filters.status) {
      whereConditions.push("u.status = ?")
      queryParams.push(filters.status)
    }

    if (filters.tipo) {
      whereConditions.push("u.tipo = ?")
      queryParams.push(filters.tipo)
    }

    if (filters.search) {
      whereConditions.push("(nome LIKE ? OR email LIKE ?)")
      queryParams.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    // Construir query
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''
    
    const query = `
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.tipo,
        u.status,
        u.saldo,
        u.data_cadastro,
        u.ultimo_acesso,
        u.gerente_id,
        u.comissao,
        u.porcentagem_comissao,
        g.nome as gerente_nome,
        COALESCE(bilhetes.total_bilhetes, 0) as total_bilhetes,
        COALESCE(bilhetes.valor_total, 0) as valor_total_apostas
      FROM usuarios u
      LEFT JOIN usuarios g ON u.gerente_id = g.id
      LEFT JOIN (
        SELECT
          usuario_id,
          COUNT(*) as total_bilhetes,
          SUM(valor_total) as valor_total
        FROM bilhetes
        WHERE status != 'cancelado'
        GROUP BY usuario_id
      ) bilhetes ON u.id = bilhetes.usuario_id
      ${whereClause}
      ORDER BY 
        CASE u.tipo
          WHEN 'admin' THEN 1
          WHEN 'gerente' THEN 2
          WHEN 'cambista' THEN 3
          WHEN 'usuario' THEN 4
        END,
        u.data_cadastro DESC
      LIMIT 100
    `

    usuarios = await executeQuery(query, queryParams)

    console.log(`📊 ${usuarios?.length || 0} usuários encontrados`)

    // Formatar usuários para o frontend
    const usuariosFormatados = (usuarios || []).map((usuario: any) => ({
      id: usuario.id,
      nome: usuario.nome,
      email: usuario.email,
      telefone: usuario.telefone || '',
      tipo: usuario.tipo,
      status: usuario.status,
      saldo: parseFloat(usuario.saldo || 0),
      data_cadastro: new Date(usuario.data_cadastro).toLocaleDateString('pt-BR'),
      ultimo_acesso: usuario.ultimo_acesso ? new Date(usuario.ultimo_acesso).toLocaleDateString('pt-BR') : 'Nunca',
      gerente_id: usuario.gerente_id,
      gerente_nome: usuario.gerente_nome || '',
      comissao: parseFloat(usuario.comissao || 0),
      porcentagem_comissao: parseFloat(usuario.porcentagem_comissao || 0),
      total_bilhetes: parseInt(usuario.total_bilhetes || 0),
      valor_total_apostas: parseFloat(usuario.valor_total_apostas || 0),
      // Adicionar informações de hierarquia
      pode_editar: canEditUser(userType, usuario.tipo),
      pode_excluir: canDeleteUser(userType, usuario.tipo)
    }))

    // Calcular estatísticas
    const stats = {
      total: usuariosFormatados.length,
      ativos: usuariosFormatados.filter(u => u.status === 'ativo').length,
      inativos: usuariosFormatados.filter(u => u.status === 'inativo').length,
      bloqueados: usuariosFormatados.filter(u => u.status === 'bloqueado').length,
      por_tipo: {
        admin: usuariosFormatados.filter(u => u.tipo === 'admin').length,
        gerente: usuariosFormatados.filter(u => u.tipo === 'gerente').length,
        cambista: usuariosFormatados.filter(u => u.tipo === 'cambista').length,
        usuario: usuariosFormatados.filter(u => u.tipo === 'usuario').length
      }
    }

    return NextResponse.json({
      success: true,
      usuarios: usuariosFormatados,
      stats,
      user_type: userType,
      total: usuariosFormatados.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar usuários filtrados:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      usuarios: []
    }, { status: 500 })
  }
}

// Função para verificar se pode editar usuário
function canEditUser(userType: string, targetType: string): boolean {
  switch (userType) {
    case 'admin':
      return true // Admin pode editar todos
    case 'gerente':
      return ['cambista', 'usuario'].includes(targetType) // Gerente pode editar cambistas e usuários
    case 'cambista':
      return targetType === 'usuario' // Cambista pode editar apenas usuários
    default:
      return false
  }
}

// Função para verificar se pode excluir usuário
function canDeleteUser(userType: string, targetType: string): boolean {
  switch (userType) {
    case 'admin':
      return targetType !== 'admin' // Admin pode excluir todos exceto outros admins
    case 'gerente':
      return ['cambista', 'usuario'].includes(targetType) // Gerente pode excluir cambistas e usuários
    case 'cambista':
      return targetType === 'usuario' // Cambista pode excluir apenas usuários
    default:
      return false
  }
}
