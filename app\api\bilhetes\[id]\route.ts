import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/mysql'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const bilheteId = params.id
    console.log(`🔍 Buscando detalhes do bilhete ID: ${bilheteId}`)

    // Buscar bilhete principal
    const bilhete = await executeQuery(`
      SELECT 
        b.*,
        u.nome as usuario_nome,
        u.email as usuario_email,
        u.tipo as usuario_tipo
      FROM bilhetes b
      LEFT JOIN usuarios u ON b.usuario_id = u.id
      WHERE b.id = ?
    `, [bilheteId])

    if (!bilhete || bilhete.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Bilhete não encontrado'
      }, { status: 404 })
    }

    const bilheteData = bilhete[0]

    // Buscar apostas do bilhete
    const apostas = await executeQuery(`
      SELECT * FROM apostas WHERE bilhete_id = ?
      ORDER BY id ASC
    `, [bilheteId])

    // Formatar dados para o frontend
    const bilheteFormatado = {
      id: bilheteData.id,
      codigo: bilheteData.codigo,
      cliente: bilheteData.usuario_nome || 'Cliente não identificado',
      telefone: bilheteData.usuario_email || '',
      valor: parseFloat(bilheteData.valor_total || 0),
      status: bilheteData.status === 'pago' ? 'ganha' : 
              bilheteData.status === 'cancelado' ? 'perdida' : 'pendente',
      data: new Date(bilheteData.created_at).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit', 
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      jogos: apostas.map((aposta: any) => 
        `${aposta.jogo} - ${aposta.resultado_apostado} (Odd: ${aposta.odd})`
      ),
      apostas: apostas.map((aposta: any) => ({
        jogo: aposta.jogo,
        resultado: aposta.resultado_apostado,
        odd: parseFloat(aposta.odd || 1),
        status: aposta.status || 'pendente'
      })),
      quantidade_apostas: apostas.length,
      created_at: bilheteData.created_at,
      updated_at: bilheteData.updated_at,
      transaction_id: bilheteData.transaction_id
    }

    console.log(`✅ Bilhete ${bilheteData.codigo} carregado com ${apostas.length} apostas`)

    return NextResponse.json({
      success: true,
      ...bilheteFormatado
    })

  } catch (error) {
    console.error('❌ Erro ao buscar bilhete:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
