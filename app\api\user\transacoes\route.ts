import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar saldo atual do usuário
    const user = await executeQuerySingle(
      "SELECT saldo FROM usuarios WHERE id = ?",
      [userId]
    )

    // Buscar transações do usuário (usar saldo_transacoes que é a tabela correta)
    const transacoes = await executeQuery(`
      SELECT
        id,
        tipo,
        valor,
        saldo_anterior,
        saldo_posterior,
        descricao,
        created_at,
        transaction_id,
        status
      FROM saldo_transacoes
      WHERE usuario_id = ?
      ORDER BY created_at DESC
      LIMIT 50
    `, [userId])

    // Se não há transações na tabela específica, buscar dos pagamentos
    let transacoesFormatadas = []
    
    if (transacoes.length === 0) {
      const pagamentos = await executeQuery(`
        SELECT
          id,
          COALESCE(valor, 0) as valor,
          status,
          COALESCE(codigo_transacao, 'Pagamento') as descricao,
          data_pagamento as created_at,
          codigo_transacao as transaction_id,
          'deposito' as tipo
        FROM pagamentos
        WHERE usuario_id = ?
        ORDER BY data_pagamento DESC
        LIMIT 50
      `, [userId])

      transacoesFormatadas = pagamentos.map((p: any) => ({
        id: p.id,
        tipo: p.tipo,
        valor: parseFloat(p.valor || 0),
        saldo_anterior: 0,
        saldo_posterior: 0,
        descricao: p.descricao || 'Transação',
        created_at: p.created_at,
        transaction_id: p.transaction_id,
        status: p.status
      }))
    } else {
      transacoesFormatadas = transacoes.map((t: any) => ({
        id: t.id,
        tipo: t.tipo,
        valor: parseFloat(t.valor || 0),
        saldo_anterior: parseFloat(t.saldo_anterior || 0),
        saldo_posterior: parseFloat(t.saldo_posterior || 0),
        descricao: t.descricao,
        created_at: t.created_at,
        transaction_id: t.transaction_id,
        status: t.status
      }))
    }

    return NextResponse.json({
      success: true,
      transacoes: transacoesFormatadas,
      saldo_atual: user ? parseFloat(user.saldo || 0) : 0
    })

  } catch (error) {
    console.error("❌ Erro ao buscar transações:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      transacoes: [],
      saldo_atual: 0
    }, { status: 500 })
  }
}
