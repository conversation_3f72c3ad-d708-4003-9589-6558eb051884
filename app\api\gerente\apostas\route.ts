import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    console.log('👨‍💼 Buscando apostas do gerente...')

    // Buscar todas as ações do sistema (bilhetes, usuários criados, etc.)
    const bilhetes = await executeQuery(`
      SELECT
        b.id,
        b.codigo,
        b.usuario_nome as cliente,
        b.usuario_email as telefone,
        b.valor_total as valor,
        b.quantidade_apostas,
        b.status,
        b.created_at as data,
        b.updated_at,
        u.nome as cambista,
        u.tipo as usuario_tipo,
        'bilhete' as tipo_acao
      FROM bilhetes b
      LEFT JOIN usuarios u ON b.usuario_id = u.id
      ORDER BY b.created_at DESC
      LIMIT 100
    `)

    // Buscar usuários criados recentemente
    const usuariosCriados = await executeQuery(`
      SELECT
        id,
        nome as cliente,
        email as telefone,
        0 as valor,
        0 as quantidade_apostas,
        status,
        data_cadastro as data,
        data_cadastro as updated_at,
        'Sistema' as cambista,
        tipo as usuario_tipo,
        'usuario_criado' as tipo_acao
      FROM usuarios
      WHERE data_cadastro >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      ORDER BY data_cadastro DESC
      LIMIT 50
    `)

    // Combinar todas as ações
    const todasAcoes = [...bilhetes, ...usuariosCriados]
      .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())
      .slice(0, 100)

    console.log(`📊 ${todasAcoes?.length || 0} ações encontradas`)

    // Formatar ações para o frontend
    const acoesFormatadas = (todasAcoes || []).map((acao: any) => {
      const dataFormatada = acao.data ? new Date(acao.data).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }) : 'Data inválida'

      if (acao.tipo_acao === 'usuario_criado') {
        return {
          id: acao.id,
          cliente: `${acao.cliente} (${acao.usuario_tipo})`,
          telefone: acao.telefone || '',
          valor: 0,
          jogos: ['Usuário criado'],
          data: dataFormatada,
          status: acao.status === 'ativo' ? 'ativo' : 'inativo',
          cambista: 'Sistema',
          codigo: `USER-${acao.id}`
        }
      } else {
        return {
          id: acao.id,
          cliente: acao.cliente || 'Cliente não identificado',
          telefone: acao.telefone || '',
          valor: parseFloat(acao.valor || 0),
          jogos: [`Bilhete com ${acao.quantidade_apostas || 0} apostas`],
          data: dataFormatada,
          status: acao.status === 'pago' ? 'ganha' :
                  acao.status === 'cancelado' ? 'perdida' : 'pendente',
          cambista: acao.cambista || 'Sistema',
          codigo: acao.codigo
        }
      }
    })

    return NextResponse.json({
      success: true,
      apostas: acoesFormatadas,
      total: acoesFormatadas.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar apostas do gerente:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      apostas: []
    }, { status: 500 })
  }
}
