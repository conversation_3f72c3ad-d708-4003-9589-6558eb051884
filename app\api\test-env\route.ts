import { NextResponse } from 'next/server'

export async function GET() {
  return NextResponse.json({
    env_check: {
      DB_HOST: process.env.DB_HOST || 'undefined',
      DB_PORT: process.env.DB_PORT || 'undefined', 
      DB_USER: process.env.DB_USER || 'undefined',
      DB_PASSWORD: process.env.DB_PASSWORD ? '[CONFIGURADA]' : 'undefined',
      DB_NAME: process.env.DB_NAME || 'undefined',
      NODE_ENV: process.env.NODE_ENV || 'undefined'
    },
    message: 'Teste de variáveis de ambiente'
  })
}
