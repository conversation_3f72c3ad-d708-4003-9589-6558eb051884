#!/usr/bin/env node

import { executeQuery, executeQuery<PERSON>ingle, initializeDatabase } from '../lib/database-config.js'

console.log('🔍 Verificando usuários no sistema...')

async function checkUsers() {
  try {
    await initializeDatabase()
    
    // Verificar se <NAME_EMAIL> existe
    const adminUser = await executeQuerySingle(
      "SELECT id, nome, email, tipo, status, data_cadastro FROM usuarios WHERE email = ?",
      ['<EMAIL>']
    )
    
    if (adminUser) {
      console.log('👤 Usuário encontrado:')
      console.log(`   ID: ${adminUser.id}`)
      console.log(`   Nome: ${adminUser.nome}`)
      console.log(`   Email: ${adminUser.email}`)
      console.log(`   Tipo: ${adminUser.tipo}`)
      console.log(`   Status: ${adminUser.status}`)
      console.log(`   Cadastro: ${adminUser.data_cadastro}`)
      
      console.log('\n💡 Para resolver o conflito, você pode:')
      console.log('1. Usar um email diferente para o novo cambista')
      console.log('2. Remover o usuário existente (se for um teste)')
      console.log('3. Atualizar o usuário existente para cambista')
      
      // Verificar se é um usuário de teste
      if (adminUser.nome.includes('test') || adminUser.nome.includes('Test') || 
          adminUser.nome.includes('teste') || adminUser.nome.includes('Teste')) {
        console.log('\n🧪 Este parece ser um usuário de teste.')
        console.log('Execute: node scripts/clean-test-users.js para removê-lo')
      }
      
    } else {
      console.log('✅ Email <EMAIL> está disponível')
    }
    
    // Mostrar todos os usuários
    console.log('\n📋 Todos os usuários no sistema:')
    const allUsers = await executeQuery(
      "SELECT id, nome, email, tipo, status FROM usuarios ORDER BY data_cadastro DESC LIMIT 10"
    )
    
    if (allUsers.length === 0) {
      console.log('   Nenhum usuário encontrado')
    } else {
      allUsers.forEach(user => {
        console.log(`   ${user.id}: ${user.nome} (${user.email}) - ${user.tipo} - ${user.status}`)
      })
    }
    
  } catch (error) {
    console.error('❌ Erro ao verificar usuários:', error)
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  checkUsers()
    .then(() => {
      console.log('\n✅ Verificação concluída')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Erro na verificação:', error)
      process.exit(1)
    })
}
