import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    // Buscar gerentes com estatísticas
    const gerentes = await executeQuery(`
      SELECT
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.usuario,
        u.comissao,
        u.observacao,
        u.pode_criar_cambista,
        u.pode_colocar_comissao,
        u.status,
        u.data_cadastro as data_criacao,
        u.tipo as nivel_acesso,
        COUNT(DISTINCT uf.id) as total_usuarios,
        0 as comissao_total
      FROM usuarios u
      LEFT JOIN usuarios uf ON u.id = uf.gerente_id
      WHERE u.tipo IN ('gerente', 'supervisor', 'admin')
      GROUP BY u.id, u.nome, u.email, u.telefone, u.usuario, u.comissao, u.observacao,
               u.pode_criar_cambista, u.pode_colocar_comissao, u.status, u.data_cadastro, u.tipo
      ORDER BY u.data_cadastro DESC
    `)

    const gerentesFormatados = gerentes.map((gerente: any) => ({
      id: gerente.id,
      nome: gerente.nome,
      email: gerente.email,
      telefone: gerente.telefone || "",
      usuario: gerente.usuario || "",
      comissao: parseFloat(gerente.comissao || 5),
      observacao: gerente.observacao || "",
      pode_criar_cambista: Boolean(gerente.pode_criar_cambista),
      pode_colocar_comissao: Boolean(gerente.pode_colocar_comissao),
      status: gerente.status,
      data_criacao: gerente.data_criacao,
      total_usuarios: parseInt(gerente.total_usuarios || 0),
      comissao_total: parseFloat(gerente.comissao_total || 0),
      nivel_acesso: gerente.nivel_acesso
    }))

    return NextResponse.json({
      success: true,
      gerentes: gerentesFormatados
    })

  } catch (error) {
    console.error("❌ Erro ao buscar gerentes:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      gerentes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      nome,
      email,
      telefone,
      usuario,
      comissao,
      senha,
      observacao,
      nivel_acesso,
      pode_criar_cambista,
      pode_colocar_comissao
    } = body

    if (!nome || !email || !senha || !nivel_acesso) {
      return NextResponse.json({
        success: false,
        error: "Nome, email, senha e nível de acesso são obrigatórios"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Verificar se email já existe
    const existingUser = await executeQuery(
      "SELECT id FROM usuarios WHERE email = ?",
      [email]
    )

    if (existingUser.length > 0) {
      return NextResponse.json({
        success: false,
        error: "Email já está em uso"
      }, { status: 400 })
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 10)

    // Inserir novo gerente
    const result = await executeQuery(`
      INSERT INTO usuarios (
        nome, email, telefone, usuario, comissao, senha_hash, observacao,
        tipo, status, pode_criar_cambista, pode_colocar_comissao, data_cadastro
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ativo', ?, ?, NOW())
    `, [
      nome,
      email,
      telefone,
      usuario || email,
      comissao || 5,
      senhaHash,
      observacao || '',
      nivel_acesso,
      pode_criar_cambista ? 1 : 0,
      pode_colocar_comissao ? 1 : 0
    ])

    return NextResponse.json({
      success: true,
      message: "Gerente criado com sucesso",
      id: (result as any).insertId
    })

  } catch (error) {
    console.error("❌ Erro ao criar gerente:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      nome,
      email,
      telefone,
      usuario,
      comissao,
      senha,
      observacao,
      nivel_acesso,
      pode_criar_cambista,
      pode_colocar_comissao
    } = body

    if (!id || !nome || !email || !nivel_acesso) {
      return NextResponse.json({
        success: false,
        error: "ID, nome, email e nível de acesso são obrigatórios"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Verificar se o gerente existe
    const existingGerente = await executeQuery(
      "SELECT id FROM usuarios WHERE id = ? AND tipo IN ('gerente', 'supervisor', 'admin')",
      [id]
    )

    if (existingGerente.length === 0) {
      return NextResponse.json({
        success: false,
        error: "Gerente não encontrado"
      }, { status: 404 })
    }

    // Verificar se email já existe em outro usuário
    const existingEmail = await executeQuery(
      "SELECT id FROM usuarios WHERE email = ? AND id != ?",
      [email, id]
    )

    if (existingEmail.length > 0) {
      return NextResponse.json({
        success: false,
        error: "Email já está em uso por outro usuário"
      }, { status: 400 })
    }

    // Preparar query de atualização
    let updateQuery = `
      UPDATE usuarios SET
        nome = ?, email = ?, telefone = ?, usuario = ?, comissao = ?,
        observacao = ?, tipo = ?, pode_criar_cambista = ?, pode_colocar_comissao = ?
    `
    let updateParams = [
      nome,
      email,
      telefone,
      usuario || email,
      comissao || 5,
      observacao || '',
      nivel_acesso,
      pode_criar_cambista ? 1 : 0,
      pode_colocar_comissao ? 1 : 0
    ]

    // Se senha foi fornecida, incluir na atualização
    if (senha && senha.trim() !== '') {
      const senhaHash = await bcrypt.hash(senha, 10)
      updateQuery += ', senha_hash = ?'
      updateParams.push(senhaHash)
    }

    updateQuery += ' WHERE id = ?'
    updateParams.push(id)

    await executeQuery(updateQuery, updateParams)

    return NextResponse.json({
      success: true,
      message: "Gerente atualizado com sucesso"
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar gerente:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}
