#!/usr/bin/env node

import { executeQuery, executeQuerySingle, initializeDatabase } from '../lib/database-config.js'

console.log('🧹 Limpando usuários de teste...')

async function cleanTestUsers() {
  try {
    await initializeDatabase()
    
    // Verificar usuários que parecem ser de teste
    const testUsers = await executeQuery(`
      SELECT id, nome, email, tipo, status, data_cadastro 
      FROM usuarios 
      WHERE 
        email = '<EMAIL>' OR
        nome LIKE '%test%' OR 
        nome LIKE '%Test%' OR 
        nome LIKE '%teste%' OR 
        nome LIKE '%Teste%' OR
        email LIKE '%test%' OR
        email LIKE '%exemplo%'
      ORDER BY data_cadastro DESC
    `)
    
    if (testUsers.length === 0) {
      console.log('✅ Nenhum usuário de teste encontrado')
      return
    }
    
    console.log(`🔍 Encontrados ${testUsers.length} usuários de teste:`)
    testUsers.forEach(user => {
      console.log(`   ${user.id}: ${user.nome} (${user.email}) - ${user.tipo}`)
    })
    
    console.log('\n⚠️ ATENÇÃO: Esta operação irá remover os usuários listados acima!')
    console.log('Pressione Ctrl+C para cancelar ou aguarde 5 segundos para continuar...')
    
    // Aguardar 5 segundos
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    console.log('\n🗑️ Removendo usuários de teste...')
    
    for (const user of testUsers) {
      try {
        // Remover apostas relacionadas primeiro (se existirem)
        await executeQuery('DELETE FROM apostas WHERE usuario_id = ?', [user.id])
        
        // Remover bilhetes relacionados (se existirem)
        await executeQuery('DELETE FROM bilhetes WHERE usuario_id = ?', [user.id])
        
        // Remover o usuário
        await executeQuery('DELETE FROM usuarios WHERE id = ?', [user.id])
        
        console.log(`✅ Removido: ${user.nome} (${user.email})`)
      } catch (error) {
        console.error(`❌ Erro ao remover ${user.nome}:`, error.message)
      }
    }
    
    console.log('\n🎉 Limpeza concluída!')
    console.log('Agora você pode criar o cambista com <NAME_EMAIL>')
    
  } catch (error) {
    console.error('❌ Erro na limpeza:', error)
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanTestUsers()
    .then(() => {
      console.log('\n✅ Script concluído')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Erro no script:', error)
      process.exit(1)
    })
}
