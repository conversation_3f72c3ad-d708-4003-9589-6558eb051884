#!/usr/bin/env node

import { FOOTBALL_API_CONFIG, getAPIStatus, logAPIStatus } from '../lib/api-config.js'

console.log('🔧 Gerenciador de Uso da API Football Data')
console.log('==========================================')

function showCurrentConfig() {
  console.log('\n📋 Configuração Atual:')
  console.log(`   🌐 URL: ${FOOTBALL_API_CONFIG.BASE_URL}`)
  console.log(`   🔑 Token: ${FOOTBALL_API_CONFIG.TOKEN ? '✅ Configurado' : '❌ Não configurado'}`)
  console.log(`   ⏱️  Max Req/min: ${FOOTBALL_API_CONFIG.MAX_REQUESTS_PER_MINUTE}`)
  console.log(`   ⏳ Delay entre req: ${FOOTBALL_API_CONFIG.REQUEST_DELAY/1000}s`)
  console.log(`   💾 Cache: ${FOOTBALL_API_CONFIG.CACHE_DURATION/60000} minutos`)
  console.log(`   🔄 Fallback local: ${FOOTBALL_API_CONFIG.USE_LOCAL_FALLBACK ? '✅' : '❌'}`)
}

function showAPIStatus() {
  console.log('\n📊 Status Atual da API:')
  logAPIStatus('(Gerenciador)')
}

async function testAPIConnection() {
  console.log('\n🧪 Testando Conexão com a API...')
  
  if (!FOOTBALL_API_CONFIG.TOKEN) {
    console.log('❌ Token não configurado!')
    return false
  }
  
  try {
    const response = await fetch(`${FOOTBALL_API_CONFIG.BASE_URL}/competitions`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_CONFIG.TOKEN,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Conexão bem-sucedida!')
      console.log(`📊 ${data.competitions?.length || 0} competições disponíveis`)
      return true
    } else {
      console.log(`❌ Erro: HTTP ${response.status}`)
      if (response.status === 429) {
        console.log('⏳ Rate limit ativo - aguarde antes de fazer mais requisições')
      }
      return false
    }
  } catch (error) {
    console.log('❌ Erro de conexão:', error.message)
    return false
  }
}

function showRecommendations() {
  console.log('\n💡 Recomendações para Otimizar o Uso:')
  console.log('')
  console.log('🎯 Para Desenvolvimento:')
  console.log('   1. Use cache local sempre que possível')
  console.log('   2. Limite requisições a competições prioritárias')
  console.log('   3. Implemente fallback para dados do banco')
  console.log('   4. Monitore o rate limit constantemente')
  console.log('')
  console.log('🚀 Para Produção:')
  console.log('   1. Considere upgrade para plano pago')
  console.log('   2. Implemente sistema de filas para requisições')
  console.log('   3. Use webhooks quando disponível')
  console.log('   4. Cache dados por períodos mais longos')
  console.log('')
  console.log('⚠️ Limites do Plano Gratuito:')
  console.log('   • 10 requisições por minuto')
  console.log('   • Dados limitados de algumas competições')
  console.log('   • Sem dados em tempo real')
  console.log('')
  console.log('💰 Benefícios do Plano Pago:')
  console.log('   • Até 100 requisições por minuto')
  console.log('   • Acesso a todas as competições')
  console.log('   • Dados em tempo real')
  console.log('   • Suporte prioritário')
}

function showQuickFixes() {
  console.log('\n🔧 Correções Rápidas para Rate Limit:')
  console.log('')
  console.log('1. 📦 Aumentar cache:')
  console.log('   - Edite CACHE_DURATION em lib/api-config.js')
  console.log('   - Recomendado: 60 minutos ou mais')
  console.log('')
  console.log('2. ⏳ Aumentar delay:')
  console.log('   - Edite REQUEST_DELAY em lib/api-config.js')
  console.log('   - Recomendado: 10-15 segundos')
  console.log('')
  console.log('3. 🎯 Reduzir competições:')
  console.log('   - Foque apenas em competições prioritárias')
  console.log('   - Use dados do banco para competições secundárias')
  console.log('')
  console.log('4. 🔄 Usar fallback:')
  console.log('   - Certifique-se que USE_LOCAL_FALLBACK está true')
  console.log('   - Popule o banco com dados básicos')
}

async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'status'
  
  switch (command) {
    case 'status':
      showCurrentConfig()
      showAPIStatus()
      break
      
    case 'test':
      showCurrentConfig()
      await testAPIConnection()
      break
      
    case 'recommendations':
      showRecommendations()
      break
      
    case 'fixes':
      showQuickFixes()
      break
      
    case 'all':
      showCurrentConfig()
      showAPIStatus()
      await testAPIConnection()
      showRecommendations()
      showQuickFixes()
      break
      
    default:
      console.log('\n📖 Comandos disponíveis:')
      console.log('   status         - Mostrar configuração e status atual')
      console.log('   test          - Testar conexão com a API')
      console.log('   recommendations - Mostrar recomendações de otimização')
      console.log('   fixes         - Mostrar correções rápidas para rate limit')
      console.log('   all           - Executar todos os comandos acima')
      console.log('')
      console.log('Exemplo: node scripts/manage-api-usage.js test')
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}
