import { NextRequest, NextResponse } from 'next/server'
import { executeQuery<PERSON>ingle, initializeDatabase } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    
    if (!email) {
      return NextResponse.json(
        {
          error: 'Email é obrigatório',
          success: false
        },
        { status: 400 }
      )
    }
    
    await initializeDatabase()
    
    // Verificar se email já existe
    const existingUser = await executeQuerySingle(
      "SELECT id, nome, email, tipo, status FROM usuarios WHERE email = ?",
      [email]
    )
    
    if (existingUser) {
      return NextResponse.json({
        available: false,
        message: `Email já está sendo usado por: ${existingUser.nome} (${existingUser.tipo})`,
        user: {
          id: existingUser.id,
          nome: existingUser.nome,
          tipo: existingUser.tipo,
          status: existingUser.status
        },
        success: true
      })
    }
    
    return NextResponse.json({
      available: true,
      message: 'Email disponível para uso',
      success: true
    })
    
  } catch (error) {
    console.error('Erro ao verificar email:', error)
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        success: false
      },
      { status: 500 }
    )
  }
}
