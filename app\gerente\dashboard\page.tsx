"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { toast } from "sonner"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DollarSign,
  TrendingUp,
  Target,
  LogOut,
  Plus,
  Calendar,
  User,
  Users,
  UserPlus,
  Settings,
  BarChart3,
  Home
} from "lucide-react"

interface Gerente {
  id: number
  nome: string
  email: string
  comissao: number
  pode_criar_cambista: boolean
  pode_colocar_comissao: boolean
}

interface Cambista {
  id: number
  nome: string
  email: string
  telefone: string
  status: string
  comissao: number
  vendas_total: number
  vendas_mes: number
}

interface Usuario {
  id: number
  nome: string
  email: string
  telefone: string
  status: string
  data_cadastro: string
  total_apostas: number
}

interface Aposta {
  id: number
  cliente: string
  telefone: string
  valor: number
  jogos: string[]
  data: string
  status: "pendente" | "ganha" | "perdida"
  cambista: string
}

export default function GerenteDashboard() {
  const [gerente, setGerente] = useState<Gerente | null>(null)
  const [cambistas, setCambistas] = useState<Cambista[]>([])
  const [usuarios, setUsuarios] = useState<Usuario[]>([])
  const [apostas, setApostas] = useState<Aposta[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateCambistaModalOpen, setIsCreateCambistaModalOpen] = useState(false)
  const [isCreateApostaModalOpen, setIsCreateApostaModalOpen] = useState(false)
  
  const [cambistaFormData, setCambistaFormData] = useState({
    nome: "",
    email: "",
    telefone: "",
    endereco: "",
    cpf_cnpj: "",
    senha: "",
    comissao: "10"
  })

  const [apostaFormData, setApostaFormData] = useState({
    cliente: "",
    telefone: "",
    valor: "",
    jogos: "",
  })

  const router = useRouter()

  useEffect(() => {
    const gerenteData = localStorage.getItem("gerente")
    if (!gerenteData) {
      router.push("/gerente/login")
      return
    }

    const parsedGerente = JSON.parse(gerenteData)
    setGerente(parsedGerente)

    // Mostrar mensagem de boas-vindas
    setTimeout(() => {
      toast.success(`📊 Bem-vindo, Gerente ${parsedGerente.nome}! Impressão automática ativada.`, {
        duration: 4000,
        style: {
          background: '#10B981',
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          padding: '12px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)'
        }
      })
    }, 1000)

    // Carregar dados do gerente
    loadGerenteData()
  }, [])

  const loadGerenteData = async () => {
    try {
      setLoading(true)

      // Carregar cambistas
      const cambistasResponse = await fetch('/api/gerente/cambistas')
      if (cambistasResponse.ok) {
        const cambistasData = await cambistasResponse.json()
        setCambistas(cambistasData.cambistas || [])
      }

      // Carregar usuários
      const usuariosResponse = await fetch('/api/gerente/usuarios')
      if (usuariosResponse.ok) {
        const usuariosData = await usuariosResponse.json()
        setUsuarios(usuariosData.usuarios || [])
      }

      // Carregar apostas
      const apostasResponse = await fetch('/api/gerente/apostas')
      if (apostasResponse.ok) {
        const apostasData = await apostasResponse.json()
        setApostas(apostasData.apostas || [])
      }

    } catch (error) {
      console.error('Erro ao carregar dados do gerente:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCambista = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const response = await fetch('/api/admin/cambistas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...cambistaFormData,
          gerente_id: gerente?.id
        }),
      })

      if (response.ok) {
        toast.success('Cambista criado com sucesso!')
        setIsCreateCambistaModalOpen(false)
        setCambistaFormData({
          nome: "",
          email: "",
          telefone: "",
          endereco: "",
          cpf_cnpj: "",
          senha: "",
          comissao: "10"
        })
        loadGerenteData()
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'Erro ao criar cambista')
      }
    } catch (error) {
      console.error('Erro ao criar cambista:', error)
      toast.error('Erro ao criar cambista')
    }
  }

  const handleCreateAposta = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const response = await fetch('/api/cambista/apostas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...apostaFormData,
          jogos: apostaFormData.jogos.split(',').map(j => j.trim()),
          cambista_id: gerente?.id
        }),
      })

      if (response.ok) {
        toast.success('Aposta criada com sucesso!')
        setIsCreateApostaModalOpen(false)
        setApostaFormData({
          cliente: "",
          telefone: "",
          valor: "",
          jogos: "",
        })
        loadGerenteData()
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'Erro ao criar aposta')
      }
    } catch (error) {
      console.error('Erro ao criar aposta:', error)
      toast.error('Erro ao criar aposta')
    }
  }

  const handleLogout = () => {
    localStorage.removeItem("gerente")
    router.push("/gerente/login")
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      ativo: "bg-green-100 text-green-800",
      inativo: "bg-gray-100 text-gray-800",
      bloqueado: "bg-red-100 text-red-800",
      pendente: "bg-yellow-100 text-yellow-800",
      ganha: "bg-green-100 text-green-800",
      perdida: "bg-red-100 text-red-800",
    }
    
    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-100 text-gray-800"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard Gerente</h1>
              <p className="text-gray-600">Bem-vindo, {gerente?.nome}</p>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => router.push('/')} variant="outline">
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
              <Button onClick={handleLogout} variant="outline">
                <LogOut className="h-4 w-4 mr-2" />
                Sair
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cambistas</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{cambistas.length}</div>
              <p className="text-xs text-muted-foreground">
                Total de cambistas
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Usuários</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{usuarios.length}</div>
              <p className="text-xs text-muted-foreground">
                Total de usuários
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Apostas</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{apostas.length}</div>
              <p className="text-xs text-muted-foreground">
                Total de apostas
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Faturamento</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                R$ {apostas.reduce((acc, aposta) => acc + aposta.valor, 0).toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                Total em apostas
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="apostas" className="space-y-4">
          <TabsList>
            <TabsTrigger value="apostas">Apostas</TabsTrigger>
            <TabsTrigger value="cambistas">Cambistas</TabsTrigger>
            <TabsTrigger value="usuarios">Usuários</TabsTrigger>
          </TabsList>

          {/* Apostas Tab */}
          <TabsContent value="apostas" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Gestão de Apostas</h2>
              <Dialog open={isCreateApostaModalOpen} onOpenChange={setIsCreateApostaModalOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Nova Aposta
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Criar Nova Aposta</DialogTitle>
                    <DialogDescription>
                      Preencha os dados para criar uma nova aposta
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleCreateAposta} className="space-y-4">
                    <div>
                      <Label htmlFor="cliente">Nome do Cliente</Label>
                      <Input
                        id="cliente"
                        value={apostaFormData.cliente}
                        onChange={(e) => setApostaFormData({...apostaFormData, cliente: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="telefone">Telefone/Email</Label>
                      <Input
                        id="telefone"
                        value={apostaFormData.telefone}
                        onChange={(e) => setApostaFormData({...apostaFormData, telefone: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="valor">Valor da Aposta</Label>
                      <Input
                        id="valor"
                        type="number"
                        step="0.01"
                        value={apostaFormData.valor}
                        onChange={(e) => setApostaFormData({...apostaFormData, valor: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="jogos">Jogos (separados por vírgula)</Label>
                      <Input
                        id="jogos"
                        value={apostaFormData.jogos}
                        onChange={(e) => setApostaFormData({...apostaFormData, jogos: e.target.value})}
                        placeholder="Ex: Flamengo x Palmeiras, Santos x Corinthians"
                        required
                      />
                    </div>
                    <Button type="submit" className="w-full">
                      Criar Aposta
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Cliente</TableHead>
                      <TableHead>Contato</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Jogos</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Cambista</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {apostas.map((aposta) => (
                      <TableRow key={aposta.id}>
                        <TableCell className="font-medium">{aposta.cliente}</TableCell>
                        <TableCell>{aposta.telefone}</TableCell>
                        <TableCell>R$ {aposta.valor.toFixed(2)}</TableCell>
                        <TableCell>{aposta.jogos.join(", ")}</TableCell>
                        <TableCell>{aposta.data}</TableCell>
                        <TableCell>{getStatusBadge(aposta.status)}</TableCell>
                        <TableCell>{aposta.cambista}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Cambistas Tab */}
          <TabsContent value="cambistas" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Gestão de Cambistas</h2>
              {gerente?.pode_criar_cambista && (
                <Dialog open={isCreateCambistaModalOpen} onOpenChange={setIsCreateCambistaModalOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Novo Cambista
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Criar Novo Cambista</DialogTitle>
                      <DialogDescription>
                        Preencha os dados para criar um novo cambista
                      </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleCreateCambista} className="space-y-4">
                      <div>
                        <Label htmlFor="nome">Nome</Label>
                        <Input
                          id="nome"
                          value={cambistaFormData.nome}
                          onChange={(e) => setCambistaFormData({...cambistaFormData, nome: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={cambistaFormData.email}
                          onChange={(e) => setCambistaFormData({...cambistaFormData, email: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="telefone">Telefone</Label>
                        <Input
                          id="telefone"
                          value={cambistaFormData.telefone}
                          onChange={(e) => setCambistaFormData({...cambistaFormData, telefone: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="endereco">Endereço</Label>
                        <Input
                          id="endereco"
                          value={cambistaFormData.endereco}
                          onChange={(e) => setCambistaFormData({...cambistaFormData, endereco: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="cpf_cnpj">CPF/CNPJ</Label>
                        <Input
                          id="cpf_cnpj"
                          value={cambistaFormData.cpf_cnpj}
                          onChange={(e) => setCambistaFormData({...cambistaFormData, cpf_cnpj: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="senha">Senha</Label>
                        <Input
                          id="senha"
                          type="password"
                          value={cambistaFormData.senha}
                          onChange={(e) => setCambistaFormData({...cambistaFormData, senha: e.target.value})}
                          required
                        />
                      </div>
                      {gerente?.pode_colocar_comissao && (
                        <div>
                          <Label htmlFor="comissao">Comissão (%)</Label>
                          <Input
                            id="comissao"
                            type="number"
                            step="0.01"
                            value={cambistaFormData.comissao}
                            onChange={(e) => setCambistaFormData({...cambistaFormData, comissao: e.target.value})}
                            required
                          />
                        </div>
                      )}
                      <Button type="submit" className="w-full">
                        Criar Cambista
                      </Button>
                    </form>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Telefone</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Comissão</TableHead>
                      <TableHead>Vendas Mês</TableHead>
                      <TableHead>Total Vendas</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cambistas.map((cambista) => (
                      <TableRow key={cambista.id}>
                        <TableCell className="font-medium">{cambista.nome}</TableCell>
                        <TableCell>{cambista.email}</TableCell>
                        <TableCell>{cambista.telefone}</TableCell>
                        <TableCell>{getStatusBadge(cambista.status)}</TableCell>
                        <TableCell>{cambista.comissao}%</TableCell>
                        <TableCell>R$ {cambista.vendas_mes.toFixed(2)}</TableCell>
                        <TableCell>R$ {cambista.vendas_total.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Usuários Tab */}
          <TabsContent value="usuarios" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Gestão de Usuários</h2>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Telefone</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Data Cadastro</TableHead>
                      <TableHead>Total Apostas</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {usuarios.map((usuario) => (
                      <TableRow key={usuario.id}>
                        <TableCell className="font-medium">{usuario.nome}</TableCell>
                        <TableCell>{usuario.email}</TableCell>
                        <TableCell>{usuario.telefone}</TableCell>
                        <TableCell>{getStatusBadge(usuario.status)}</TableCell>
                        <TableCell>{usuario.data_cadastro}</TableCell>
                        <TableCell>{usuario.total_apostas}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
