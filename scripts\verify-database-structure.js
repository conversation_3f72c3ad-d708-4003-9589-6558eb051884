import { initializeDatabase, executeQuery } from '../lib/database-config.js'
import fs from 'fs'

async function verifyDatabaseStructure() {
  try {
    console.log('🔍 Verificando estrutura completa do banco de dados...')
    console.log('⚠️ MODO SOMENTE LEITURA - Nenhuma alteração será feita')
    
    await initializeDatabase()
    
    const dbName = process.env.DB_NAME || 'sistema-bolao-top'
    console.log(`📊 Banco de dados: ${dbName}`)
    
    // 1. Listar todas as tabelas
    console.log('\n📋 Listando todas as tabelas...')
    const tables = await executeQuery(`
      SELECT TABLE_NAME, ENGINE, TABLE_COLLATION, TABLE_ROWS, 
             ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'SIZE_MB'
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      ORDER BY TABLE_NAME
    `, [dbName])
    
    console.log(`\n📊 Total de tabelas encontradas: ${tables.length}`)
    
    let exportScript = `-- =====================================================\n`
    exportScript += `-- SCRIPT DE EXPORTAÇÃO DO BANCO DE DADOS\n`
    exportScript += `-- Banco: ${dbName}\n`
    exportScript += `-- Data: ${new Date().toLocaleString('pt-BR')}\n`
    exportScript += `-- Total de tabelas: ${tables.length}\n`
    exportScript += `-- =====================================================\n\n`
    
    exportScript += `-- Criar banco de dados se não existir\n`
    exportScript += `CREATE DATABASE IF NOT EXISTS \`${dbName}\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n`
    exportScript += `USE \`${dbName}\`;\n\n`
    
    exportScript += `-- Desabilitar verificações temporariamente\n`
    exportScript += `SET FOREIGN_KEY_CHECKS = 0;\n`
    exportScript += `SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";\n`
    exportScript += `SET AUTOCOMMIT = 0;\n`
    exportScript += `START TRANSACTION;\n\n`
    
    // 2. Para cada tabela, obter estrutura completa
    for (const table of tables) {
      const tableName = table.TABLE_NAME
      console.log(`\n🔍 Analisando tabela: ${tableName}`)
      console.log(`   Engine: ${table.ENGINE}`)
      console.log(`   Collation: ${table.TABLE_COLLATION}`)
      console.log(`   Registros: ${table.TABLE_ROWS}`)
      console.log(`   Tamanho: ${table.SIZE_MB} MB`)
      
      exportScript += `-- =====================================================\n`
      exportScript += `-- Tabela: ${tableName}\n`
      exportScript += `-- =====================================================\n\n`
      
      // Obter CREATE TABLE
      const createTable = await executeQuery(`SHOW CREATE TABLE \`${tableName}\``)
      exportScript += `DROP TABLE IF EXISTS \`${tableName}\`;\n`
      exportScript += `${createTable[0]['Create Table']};\n\n`
      
      // Obter colunas detalhadas
      const columns = await executeQuery(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, 
               EXTRA, COLUMN_KEY, CHARACTER_SET_NAME, COLLATION_NAME,
               COLUMN_TYPE, COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `, [dbName, tableName])
      
      exportScript += `-- Colunas da tabela ${tableName}:\n`
      columns.forEach(col => {
        exportScript += `-- ${col.COLUMN_NAME}: ${col.COLUMN_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`
        if (col.COLUMN_DEFAULT !== null) {
          exportScript += ` DEFAULT ${col.COLUMN_DEFAULT}`
        }
        if (col.EXTRA) {
          exportScript += ` ${col.EXTRA}`
        }
        if (col.COLUMN_KEY) {
          exportScript += ` (${col.COLUMN_KEY})`
        }
        exportScript += `\n`
      })
      exportScript += `\n`
      
      // Obter índices
      const indexes = await executeQuery(`SHOW INDEX FROM \`${tableName}\``)
      if (indexes.length > 0) {
        exportScript += `-- Índices da tabela ${tableName}:\n`
        const indexGroups = {}
        indexes.forEach(idx => {
          if (!indexGroups[idx.Key_name]) {
            indexGroups[idx.Key_name] = []
          }
          indexGroups[idx.Key_name].push(idx)
        })
        
        Object.keys(indexGroups).forEach(keyName => {
          const indexCols = indexGroups[keyName].map(i => i.Column_name).join(', ')
          const indexType = indexGroups[keyName][0].Index_type
          const isUnique = indexGroups[keyName][0].Non_unique === 0 ? 'UNIQUE' : ''
          exportScript += `-- ${isUnique} INDEX ${keyName} (${indexCols}) USING ${indexType}\n`
        })
        exportScript += `\n`
      }
      
      // Obter foreign keys
      const foreignKeys = await executeQuery(`
        SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND REFERENCED_TABLE_NAME IS NOT NULL
      `, [dbName, tableName])
      
      if (foreignKeys.length > 0) {
        exportScript += `-- Foreign Keys da tabela ${tableName}:\n`
        foreignKeys.forEach(fk => {
          exportScript += `-- ${fk.CONSTRAINT_NAME}: ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}\n`
        })
        exportScript += `\n`
      }
      
      // Contar registros
      const count = await executeQuery(`SELECT COUNT(*) as total FROM \`${tableName}\``)
      exportScript += `-- Total de registros: ${count[0].total}\n\n`
    }
    
    // 3. Obter informações sobre views, procedures, functions
    console.log('\n🔍 Verificando views, procedures e functions...')
    
    const views = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.VIEWS 
      WHERE TABLE_SCHEMA = ?
    `, [dbName])
    
    if (views.length > 0) {
      exportScript += `-- =====================================================\n`
      exportScript += `-- VIEWS (${views.length})\n`
      exportScript += `-- =====================================================\n\n`
      
      for (const view of views) {
        const createView = await executeQuery(`SHOW CREATE VIEW \`${view.TABLE_NAME}\``)
        exportScript += `DROP VIEW IF EXISTS \`${view.TABLE_NAME}\`;\n`
        exportScript += `${createView[0]['Create View']};\n\n`
      }
    }
    
    const procedures = await executeQuery(`
      SELECT ROUTINE_NAME 
      FROM INFORMATION_SCHEMA.ROUTINES 
      WHERE ROUTINE_SCHEMA = ? AND ROUTINE_TYPE = 'PROCEDURE'
    `, [dbName])
    
    if (procedures.length > 0) {
      exportScript += `-- =====================================================\n`
      exportScript += `-- STORED PROCEDURES (${procedures.length})\n`
      exportScript += `-- =====================================================\n\n`
      
      for (const proc of procedures) {
        const createProc = await executeQuery(`SHOW CREATE PROCEDURE \`${proc.ROUTINE_NAME}\``)
        exportScript += `DROP PROCEDURE IF EXISTS \`${proc.ROUTINE_NAME}\`;\n`
        exportScript += `${createProc[0]['Create Procedure']};\n\n`
      }
    }
    
    const functions = await executeQuery(`
      SELECT ROUTINE_NAME 
      FROM INFORMATION_SCHEMA.ROUTINES 
      WHERE ROUTINE_SCHEMA = ? AND ROUTINE_TYPE = 'FUNCTION'
    `, [dbName])
    
    if (functions.length > 0) {
      exportScript += `-- =====================================================\n`
      exportScript += `-- FUNCTIONS (${functions.length})\n`
      exportScript += `-- =====================================================\n\n`
      
      for (const func of functions) {
        const createFunc = await executeQuery(`SHOW CREATE FUNCTION \`${func.ROUTINE_NAME}\``)
        exportScript += `DROP FUNCTION IF EXISTS \`${func.ROUTINE_NAME}\`;\n`
        exportScript += `${createFunc[0]['Create Function']};\n\n`
      }
    }
    
    // Finalizar script
    exportScript += `-- =====================================================\n`
    exportScript += `-- FINALIZAÇÃO\n`
    exportScript += `-- =====================================================\n\n`
    exportScript += `-- Reabilitar verificações\n`
    exportScript += `SET FOREIGN_KEY_CHECKS = 1;\n`
    exportScript += `COMMIT;\n\n`
    exportScript += `-- Script gerado em: ${new Date().toLocaleString('pt-BR')}\n`
    
    // Salvar script de exportação
    const exportFileName = `database-export-${dbName}-${new Date().toISOString().slice(0, 10)}.sql`
    fs.writeFileSync(exportFileName, exportScript)
    
    console.log('\n' + '='.repeat(60))
    console.log('📊 RELATÓRIO COMPLETO DA ESTRUTURA DO BANCO')
    console.log('='.repeat(60))
    console.log(`🗄️ Banco de dados: ${dbName}`)
    console.log(`📋 Total de tabelas: ${tables.length}`)
    console.log(`👁️ Views: ${views.length}`)
    console.log(`⚙️ Procedures: ${procedures.length}`)
    console.log(`🔧 Functions: ${functions.length}`)
    
    const totalSize = tables.reduce((sum, table) => sum + parseFloat(table.SIZE_MB || 0), 0)
    console.log(`💾 Tamanho total: ${totalSize.toFixed(2)} MB`)
    
    console.log(`\n📄 Script de exportação salvo: ${exportFileName}`)
    
    console.log('\n📋 Lista de tabelas:')
    tables.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table.TABLE_NAME} (${table.TABLE_ROWS} registros, ${table.SIZE_MB} MB)`)
    })
    
    if (views.length > 0) {
      console.log('\n👁️ Views encontradas:')
      views.forEach((view, index) => {
        console.log(`   ${index + 1}. ${view.TABLE_NAME}`)
      })
    }
    
    console.log('\n✅ VERIFICAÇÃO CONCLUÍDA!')
    console.log('📄 Script SQL completo gerado para exportação/importação')
    console.log('⚠️ Nenhuma alteração foi feita no banco de dados')
    
  } catch (error) {
    console.error('❌ Erro ao verificar estrutura do banco:', error)
    process.exit(1)
  }
}

verifyDatabaseStructure()
