import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    console.log('👨‍💼 Buscando cambistas do gerente...')

    // Buscar cambistas
    const cambistas = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.status,
        u.porcentagem_comissao as comissao,
        u.data_cadastro,
        COALESCE(vendas_mes.total_mes, 0) as vendas_mes,
        COALESCE(vendas_total.total_geral, 0) as vendas_total
      FROM usuarios u
      LEFT JOIN (
        SELECT 
          usuario_id,
          SUM(valor_total) as total_mes
        FROM bilhetes 
        WHERE MONTH(created_at) = MONTH(CURDATE()) 
        AND YEAR(created_at) = YEAR(CURDATE())
        GROUP BY usuario_id
      ) vendas_mes ON u.id = vendas_mes.usuario_id
      LEFT JOIN (
        SELECT 
          usuario_id,
          SUM(valor_total) as total_geral
        FROM bilhetes 
        GROUP BY usuario_id
      ) vendas_total ON u.id = vendas_total.usuario_id
      WHERE u.tipo = 'cambista'
      ORDER BY u.data_cadastro DESC
    `)

    console.log(`📊 ${cambistas?.length || 0} cambistas encontrados`)

    // Formatar cambistas para o frontend
    const cambistasFormatados = (cambistas || []).map((cambista: any) => ({
      id: cambista.id,
      nome: cambista.nome,
      email: cambista.email,
      telefone: cambista.telefone || '',
      status: cambista.status,
      comissao: parseFloat(cambista.comissao || 10),
      vendas_mes: parseFloat(cambista.vendas_mes || 0),
      vendas_total: parseFloat(cambista.vendas_total || 0),
      data_cadastro: new Date(cambista.data_cadastro).toLocaleDateString('pt-BR')
    }))

    return NextResponse.json({
      success: true,
      cambistas: cambistasFormatados,
      total: cambistasFormatados.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar cambistas do gerente:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      cambistas: []
    }, { status: 500 })
  }
}
