// Teste para verificar se as variáveis de ambiente estão sendo carregadas

import dotenv from 'dotenv'

// Carregar .env
dotenv.config()

console.log('🔍 Verificando variáveis de ambiente...')
console.log('')

console.log('📊 Configurações do Banco:')
console.log(`   DB_HOST: "${process.env.DB_HOST || 'undefined'}"`)
console.log(`   DB_PORT: "${process.env.DB_PORT || 'undefined'}"`)
console.log(`   DB_USER: "${process.env.DB_USER || 'undefined'}"`)
console.log(`   DB_PASSWORD: "${process.env.DB_PASSWORD || 'undefined'}"`)
console.log(`   DB_NAME: "${process.env.DB_NAME || 'undefined'}"`)

console.log('')
console.log('🏈 Configurações da API Football:')
console.log(`   FOOTBALL_API_URL: "${process.env.FOOTBALL_API_URL || 'undefined'}"`)
console.log(`   FOOTBALL_API_TOKEN: "${process.env.FOOTBALL_API_TOKEN ? 'CONFIGURADO' : 'undefined'}"`)

console.log('')
console.log('⚙️ Configurações Otimizadas:')
console.log(`   FOOTBALL_API_CONSERVATIVE_MODE: "${process.env.FOOTBALL_API_CONSERVATIVE_MODE || 'undefined'}"`)
console.log(`   FOOTBALL_API_MAX_REQUESTS: "${process.env.FOOTBALL_API_MAX_REQUESTS || 'undefined'}"`)
console.log(`   FOOTBALL_API_DELAY: "${process.env.FOOTBALL_API_DELAY || 'undefined'}"`)

// Teste de conexão com MySQL
console.log('')
console.log('🔌 Testando conexão MySQL...')

import mysql from 'mysql2/promise'

const testConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top'
}

console.log('📋 Configuração que será usada:')
console.log(`   Host: ${testConfig.host}`)
console.log(`   Port: ${testConfig.port}`)
console.log(`   User: ${testConfig.user}`)
console.log(`   Password: ${testConfig.password ? '[CONFIGURADA]' : '[VAZIA]'}`)
console.log(`   Database: ${testConfig.database}`)

try {
  const connection = await mysql.createConnection(testConfig)
  await connection.ping()
  console.log('✅ Conexão MySQL bem-sucedida!')
  
  // Testar uma query simples
  const [result] = await connection.execute('SELECT 1 as test')
  console.log('✅ Query de teste executada com sucesso!')
  
  await connection.end()
} catch (error) {
  console.log('❌ Erro na conexão MySQL:')
  console.log(`   Código: ${error.code}`)
  console.log(`   Mensagem: ${error.message}`)
  
  if (error.code === 'ER_ACCESS_DENIED_ERROR') {
    console.log('')
    console.log('💡 Soluções para erro de acesso:')
    console.log('   1. Verificar se o usuário MySQL existe')
    console.log('   2. Verificar se a senha está correta')
    console.log('   3. Verificar permissões do usuário')
    console.log('   4. Tentar conectar sem senha (desenvolvimento)')
  }
}
