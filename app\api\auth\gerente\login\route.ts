import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const { email, senha } = await request.json()

    if (!email || !senha) {
      return NextResponse.json({
        success: false,
        error: 'Email e senha são obrigatórios'
      }, { status: 400 })
    }

    await initializeDatabase()

    console.log('🔐 Tentativa de login do gerente:', email)

    // Buscar gerente no banco
    const gerentes = await executeQuery(`
      SELECT
        id, nome, email, senha_hash, tipo, status,
        comissao, pode_criar_cambista, pode_colocar_comissao
      FROM usuarios
      WHERE email = ? AND tipo = 'gerente'
    `, [email])

    if (!gerentes || gerentes.length === 0) {
      console.log('❌ Gerente não encontrado:', email)
      return NextResponse.json({
        success: false,
        error: 'Credenciais inválidas'
      }, { status: 401 })
    }

    const gerente = gerentes[0] as any

    // Verificar se o gerente está ativo
    if (gerente.status !== 'ativo') {
      console.log('❌ Gerente inativo:', email)
      return NextResponse.json({
        success: false,
        error: 'Conta inativa. Entre em contato com o administrador.'
      }, { status: 401 })
    }

    // Verificar senha
    let senhaValida = false

    if (gerente.senha_hash) {
      // Se a senha está hasheada
      if (gerente.senha_hash.startsWith('$2')) {
        senhaValida = await bcrypt.compare(senha, gerente.senha_hash)
      } else {
        // Senha em texto plano (para compatibilidade)
        senhaValida = senha === gerente.senha_hash
      }
    }

    if (!senhaValida) {
      console.log('❌ Senha inválida para gerente:', email)
      return NextResponse.json({
        success: false,
        error: 'Credenciais inválidas'
      }, { status: 401 })
    }

    console.log('✅ Login do gerente realizado com sucesso:', email)

    // Retornar dados do gerente (sem a senha)
    const gerenteData = {
      id: gerente.id,
      nome: gerente.nome,
      email: gerente.email,
      tipo: gerente.tipo,
      status: gerente.status,
      comissao: parseFloat(gerente.comissao || 10),
      pode_criar_cambista: Boolean(gerente.pode_criar_cambista),
      pode_colocar_comissao: Boolean(gerente.pode_colocar_comissao)
    }

    return NextResponse.json({
      success: true,
      message: 'Login realizado com sucesso',
      gerente: gerenteData
    })

  } catch (error) {
    console.error('❌ Erro no login do gerente:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
