import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    console.log('📊 Buscando sessões ativas do sistema...')

    // Buscar todos os usuários do sistema
    const usuarios = await executeQuery(`
      SELECT
        id,
        nome,
        email,
        tipo,
        status,
        data_cadastro
      FROM usuarios
      WHERE tipo IN ('cambista', 'gerente', 'admin')
      ORDER BY
        CASE tipo
          WHEN 'admin' THEN 1
          WHEN 'gerente' THEN 2
          WHEN 'cambista' THEN 3
        END,
        nome ASC
    `)

    console.log(`📊 ${usuarios?.length || 0} usuários encontrados`)

    // Simular dados de sessão (em produção, usar dados reais do banco)
    const sessoes = (usuarios || []).map((usuario: any) => {
      // Simular status online/offline aleatório para demonstração
      const isOnline = Math.random() > 0.5
      const tempoSessao = isOnline ? Math.floor(Math.random() * 7200) : Math.floor(Math.random() * 3600)

      return {
        id: usuario.id,
        nome: usuario.nome,
        email: usuario.email,
        tipo: usuario.tipo,
        status: usuario.status,
        isOnline: isOnline,
        sessionStart: isOnline ? new Date(Date.now() - tempoSessao * 1000).toISOString() : null,
        tempoSessao: tempoSessao,
        ultimoLogin: new Date(Date.now() - Math.random() * 86400000).toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        dataCadastro: new Date(usuario.data_cadastro).toLocaleDateString('pt-BR')
      }
    })

    return NextResponse.json({
      success: true,
      sessoes: sessoes,
      total: sessoes.length,
      online: sessoes.filter(s => s.isOnline).length,
      offline: sessoes.filter(s => !s.isOnline).length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar sessões:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro interno do servidor'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, action } = await request.json()
    console.log(`🔄 Simulando atualização de sessão: ${action} para usuário ${userId}`)

    // Em produção, aqui seria feita a atualização real no banco
    // Por enquanto, apenas simular o sucesso

    return NextResponse.json({
      success: true,
      message: `Sessão ${action} registrada com sucesso`
    })

  } catch (error) {
    console.error('❌ Erro ao registrar sessão:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
