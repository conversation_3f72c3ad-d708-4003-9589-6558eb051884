CREATE TABLE `usuarios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `usuario` varchar(255) DEFAULT NULL,
  `comissao` decimal(5,2) DEFAULT 5.00,
  `observacao` text DEFAULT NULL,
  `pode_criar_cambista` tinyint(1) DEFAULT 0,
  `pode_colocar_comissao` tinyint(1) DEFAULT 0,
  `gerente_id` int(11) DEFAULT NULL,
  `endereco` text DEFAULT NULL,
  `cpf_cnpj` varchar(20) DEFAULT NULL,
  `senha_hash` varchar(255) NOT NULL,
  `tipo` enum('admin','usuario','cambista','gerente','supervisor') DEFAULT 'usuario',
  `status` enum('ativo','inativo','bloqueado') DEFAULT 'ativo',
  `saldo` decimal(10,2) DEFAULT 0.00,
  `data_cadastro` timestamp NOT NULL DEFAULT current_timestamp(),
  `ultimo_acesso` timestamp NULL DEFAULT NULL,
  `data_atualizacao` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `afiliado_id` int(11) DEFAULT NULL,
  `porcentagem_comissao` decimal(5,2) DEFAULT 0.00,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_status` (`status`),
  KEY `idx_afiliado_id` (`afiliado_id`),
  KEY `idx_saldo` (`saldo`)
) ENGINE=InnoDB AUTO_INCREMENT=603 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;