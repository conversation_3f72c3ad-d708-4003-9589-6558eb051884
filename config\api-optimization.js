// Configuração otimizada para reduzir rate limit da API Football Data

export const OPTIMIZED_CONFIG = {
  // Rate limiting ultra conservador
  MAX_REQUESTS_PER_MINUTE: 5, // Muito conservador
  REQUEST_DELAY: 15000, // 15 segundos entre requisições
  
  // Cache agressivo
  CACHE_DURATION: 2 * 60 * 60 * 1000, // 2 horas
  
  // Competições prioritárias (apenas as mais importantes)
  PRIORITY_COMPETITIONS: ['PL', 'PD'], // Apenas Premier League e La Liga
  
  // Modo de desenvolvimento - usar principalmente dados locais
  DEVELOPMENT_MODE: {
    USE_API: false, // Desabilitar API em desenvolvimento
    USE_MOCK_DATA: true, // Usar dados mock
    USE_LOCAL_ONLY: true // Usar apenas dados do banco
  },
  
  // Configuração de fallback
  FALLBACK_CONFIG: {
    ALWAYS_TRY_LOCAL_FIRST: true,
    ONLY_USE_API_IF_NO_LOCAL_DATA: true,
    MAX_API_CALLS_PER_SESSION: 3
  }
}

// Dados mock para desenvolvimento
export const MOCK_COMPETITIONS = [
  {
    id: 1,
    name: 'Premier League',
    code: 'PL',
    area: { name: 'England' },
    currentSeason: { startDate: '2024-08-17', endDate: '2025-05-25' }
  },
  {
    id: 2,
    name: 'La Liga',
    code: 'PD', 
    area: { name: 'Spain' },
    currentSeason: { startDate: '2024-08-17', endDate: '2025-05-25' }
  },
  {
    id: 3,
    name: 'Campeonato Brasileiro',
    code: 'BSA',
    area: { name: 'Brazil' },
    currentSeason: { startDate: '2024-04-13', endDate: '2024-12-08' }
  }
]

export const MOCK_MATCHES = [
  {
    id: 1,
    competition: { name: 'Premier League', code: 'PL' },
    homeTeam: { name: 'Manchester United', shortName: 'MUN' },
    awayTeam: { name: 'Liverpool', shortName: 'LIV' },
    utcDate: '2025-01-30T15:00:00Z',
    status: 'SCHEDULED',
    score: { fullTime: { home: null, away: null } }
  },
  {
    id: 2,
    competition: { name: 'La Liga', code: 'PD' },
    homeTeam: { name: 'Real Madrid', shortName: 'RMA' },
    awayTeam: { name: 'Barcelona', shortName: 'BAR' },
    utcDate: '2025-01-31T20:00:00Z',
    status: 'SCHEDULED',
    score: { fullTime: { home: null, away: null } }
  }
]

// Função para aplicar configuração otimizada
export function applyOptimizedConfig() {
  console.log('🔧 Aplicando configuração otimizada para reduzir rate limit...')
  
  // Definir variáveis de ambiente temporárias
  process.env.FOOTBALL_API_CONSERVATIVE_MODE = 'true'
  process.env.FOOTBALL_API_MAX_REQUESTS = '5'
  process.env.FOOTBALL_API_DELAY = '15000'
  process.env.FOOTBALL_API_CACHE_DURATION = '7200000' // 2 horas
  
  console.log('✅ Configuração otimizada aplicada:')
  console.log('   • Max requisições: 5/minuto')
  console.log('   • Delay: 15 segundos')
  console.log('   • Cache: 2 horas')
  console.log('   • Modo conservador ativado')
}

// Função para usar apenas dados locais
export function enableLocalOnlyMode() {
  console.log('🏠 Ativando modo apenas dados locais...')
  
  process.env.FOOTBALL_API_DISABLED = 'true'
  process.env.USE_LOCAL_DATA_ONLY = 'true'
  
  console.log('✅ Modo local ativado - API externa desabilitada')
}

// Função para verificar se deve usar API
export function shouldUseAPI() {
  // Se está em modo de desenvolvimento, evitar API
  if (process.env.NODE_ENV === 'development') {
    return false
  }
  
  // Se API está explicitamente desabilitada
  if (process.env.FOOTBALL_API_DISABLED === 'true') {
    return false
  }
  
  // Se está em modo conservador, usar com muito cuidado
  if (process.env.FOOTBALL_API_CONSERVATIVE_MODE === 'true') {
    return Math.random() < 0.1 // Apenas 10% de chance
  }
  
  return true
}

// Função para obter dados (mock ou real)
export function getCompetitionsData() {
  if (!shouldUseAPI()) {
    console.log('📦 Usando dados mock de competições')
    return { competitions: MOCK_COMPETITIONS }
  }
  
  return null // Usar API real
}

export function getMatchesData() {
  if (!shouldUseAPI()) {
    console.log('📦 Usando dados mock de partidas')
    return { matches: MOCK_MATCHES }
  }
  
  return null // Usar API real
}
