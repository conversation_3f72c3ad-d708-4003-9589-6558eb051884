'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { UserCog, Plus, Edit, Trash2, Shield, Users, DollarSign } from 'lucide-react'
import { toast } from 'sonner'

interface Gerente {
  id: number
  nome: string
  email: string
  telefone: string
  usuario?: string
  comissao?: number
  observacao?: string
  pode_criar_cambista?: boolean
  pode_colocar_comissao?: boolean
  status: 'ativo' | 'inativo'
  data_criacao: string
  total_usuarios: number
  comissao_total: number
  nivel_acesso: 'gerente' | 'supervisor'
}

export default function GerentePage() {
  const [gerentes, setGerentes] = useState<Gerente[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingGerente, setEditingGerente] = useState<Gerente | null>(null)

  useEffect(() => {
    fetchGerentes()
  }, [])

  const fetchGerentes = async () => {
    try {
      setLoading(true)
      // Buscar gerentes reais via API
      const response = await fetch('/api/admin/gerentes')
      const data = await response.json()

      if (data.success && data.gerentes) {
        setGerentes(data.gerentes)
      } else {
        console.error('Erro ao carregar gerentes:', data.error)
        setGerentes([])
      }
    } catch (error) {
      console.error('Erro ao buscar gerentes:', error)
      toast.error('Erro ao carregar gerentes')
      setGerentes([])
    } finally {
      setLoading(false)
    }
  }

  const handleSaveGerente = async (formData: FormData) => {
    try {
      const gerenteData = {
        ...(editingGerente && { id: editingGerente.id }),
        nome: formData.get('nome') as string,
        email: formData.get('email') as string,
        telefone: formData.get('telefone') as string,
        usuario: formData.get('usuario') as string,
        comissao: parseFloat(formData.get('comissao') as string),
        senha: formData.get('senha') as string,
        observacao: formData.get('observacao') as string,
        nivel_acesso: formData.get('nivel_acesso') as string,
        pode_criar_cambista: formData.get('pode_criar_cambista') === 'on',
        pode_colocar_comissao: formData.get('pode_colocar_comissao') === 'on'
      }

      const url = editingGerente ? `/api/admin/gerentes/${editingGerente.id}` : '/api/admin/gerentes'
      const method = editingGerente ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(gerenteData)
      })

      const data = await response.json()

      if (data.success) {
        toast.success(editingGerente ? 'Gerente atualizado com sucesso!' : 'Gerente criado com sucesso!')
        setDialogOpen(false)
        setEditingGerente(null)
        fetchGerentes()
      } else {
        toast.error(data.error || 'Erro ao salvar gerente')
      }
    } catch (error) {
      console.error('Erro ao salvar gerente:', error)
      toast.error('Erro ao salvar gerente')
    }
  }

  const handleDeleteGerente = async (id: number) => {
    if (confirm('Tem certeza que deseja excluir este gerente?')) {
      try {
        // Simular exclusão
        toast.success('Gerente excluído com sucesso!')
        fetchGerentes()
      } catch (error) {
        toast.error('Erro ao excluir gerente')
      }
    }
  }

  const toggleStatus = async (id: number, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'ativo' ? 'inativo' : 'ativo'
      // Simular mudança de status
      toast.success(`Gerente ${newStatus === 'ativo' ? 'ativado' : 'desativado'} com sucesso!`)
      fetchGerentes()
    } catch (error) {
      toast.error('Erro ao alterar status')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando gerentes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">👥 Gestão de Gerentes</h1>
          <p className="text-gray-600 mt-2">Controle e administração de gerentes do sistema</p>
        </div>
        
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingGerente(null)}>
              <Plus className="h-4 w-4 mr-2" />
              Novo Gerente
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingGerente ? 'Editar Gerente' : 'Novo Gerente'}</DialogTitle>
              <DialogDescription>
                {editingGerente ? 'Edite as informações do gerente' : 'Preencha os dados do novo gerente'}
              </DialogDescription>
            </DialogHeader>
            <form action={handleSaveGerente} className="space-y-4">
              <div>
                <Label htmlFor="nome">Nome Completo</Label>
                <Input id="nome" name="nome" defaultValue={editingGerente?.nome} required />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" name="email" type="email" defaultValue={editingGerente?.email} required />
              </div>
              <div>
                <Label htmlFor="telefone">Telefone</Label>
                <Input id="telefone" name="telefone" defaultValue={editingGerente?.telefone} required />
              </div>
              <div>
                <Label htmlFor="usuario">Usuário</Label>
                <Input id="usuario" name="usuario" defaultValue={editingGerente?.usuario || editingGerente?.email} required />
              </div>
              <div>
                <Label htmlFor="comissao">Comissão (%)</Label>
                <Input id="comissao" name="comissao" type="number" step="0.01" min="0" max="100" defaultValue={editingGerente?.comissao?.toString() || "5"} required />
              </div>
              <div>
                <Label htmlFor="senha">Senha</Label>
                <Input id="senha" name="senha" type="password" required={!editingGerente} placeholder={editingGerente ? "Deixe em branco para manter a senha atual" : ""} />
              </div>
              <div>
                <Label htmlFor="observacao">Observação</Label>
                <textarea
                  id="observacao"
                  name="observacao"
                  className="w-full p-2 border rounded min-h-[80px]"
                  placeholder="francildo Ribeiro de Sousa primavera do leste MT"
                  defaultValue={editingGerente?.observacao || ""}
                />
              </div>
              <div>
                <Label htmlFor="nivel_acesso">Nível de Acesso</Label>
                <select id="nivel_acesso" name="nivel_acesso" className="w-full p-2 border rounded" defaultValue={editingGerente?.nivel_acesso || 'gerente'}>
                  <option value="gerente">Gerente</option>
                  <option value="supervisor">Supervisor</option>
                </select>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="pode_criar_cambista"
                    name="pode_criar_cambista"
                    className="rounded"
                    defaultChecked={editingGerente?.pode_criar_cambista || false}
                  />
                  <Label htmlFor="pode_criar_cambista">Pode criar cambista</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="pode_colocar_comissao"
                    name="pode_colocar_comissao"
                    className="rounded"
                    defaultChecked={editingGerente?.pode_colocar_comissao || false}
                  />
                  <Label htmlFor="pode_colocar_comissao">Pode colocar comissão para cabista</Label>
                </div>
              </div>
              <Button type="submit" className="w-full">
                {editingGerente ? 'Atualizar' : 'Criar'} Gerente
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <UserCog className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{gerentes.length}</p>
                <p className="text-sm text-gray-600">Total de Gerentes</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{gerentes.filter(g => g.status === 'ativo').length}</p>
                <p className="text-sm text-gray-600">Gerentes Ativos</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{gerentes.reduce((acc, g) => acc + g.total_usuarios, 0)}</p>
                <p className="text-sm text-gray-600">Usuários Gerenciados</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  R$ {gerentes.reduce((acc, g) => acc + g.comissao_total, 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
                <p className="text-sm text-gray-600">Comissões Totais</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Gerentes */}
      <div className="grid gap-4">
        {gerentes.map((gerente) => (
          <Card key={gerente.id}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <UserCog className="h-6 w-6 text-blue-600" />
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{gerente.nome}</h3>
                    <p className="text-sm text-gray-600">{gerente.email}</p>
                    <p className="text-sm text-gray-500">{gerente.telefone}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <Badge variant={gerente.nivel_acesso === 'supervisor' ? 'default' : gerente.nivel_acesso === 'admin' ? 'destructive' : 'secondary'}>
                      {gerente.nivel_acesso === 'supervisor' ? 'Supervisor' :
                       gerente.nivel_acesso === 'admin' ? 'Administrador' :
                       gerente.nivel_acesso === 'gerente' ? 'Gerente' :
                       gerente.nivel_acesso || 'Usuário'}
                    </Badge>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-lg font-semibold text-blue-600">{gerente.total_usuarios}</p>
                    <p className="text-xs text-gray-500">Usuários</p>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-lg font-semibold text-green-600">
                      R$ {gerente.comissao_total.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </p>
                    <p className="text-xs text-gray-500">Comissão</p>
                  </div>
                  
                  <div className="text-center">
                    <Badge variant={gerente.status === 'ativo' ? 'default' : 'secondary'}>
                      {gerente.status === 'ativo' ? 'Ativo' : 'Inativo'}
                    </Badge>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingGerente(gerente)
                        setDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant={gerente.status === 'ativo' ? 'secondary' : 'default'}
                      size="sm"
                      onClick={() => toggleStatus(gerente.id, gerente.status)}
                    >
                      {gerente.status === 'ativo' ? 'Desativar' : 'Ativar'}
                    </Button>
                    
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteGerente(gerente.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
