#!/usr/bin/env node

import mysql from 'mysql2/promise'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configuração do banco de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4',
  timezone: '+00:00'
}

console.log('🔧 Iniciando correção dos problemas do banco de dados...')

async function checkAndFixDatabase() {
  let connection = null
  
  try {
    // Conectar ao banco
    console.log('📡 Conectando ao banco de dados...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Conexão estabelecida com sucesso')

    // Verificar tabelas existentes
    console.log('🔍 Verificando tabelas existentes...')
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [dbConfig.database])

    const existingTables = tables.map(t => t.TABLE_NAME)
    console.log('📋 Tabelas encontradas:', existingTables)

    // Lista de tabelas necessárias
    const requiredTables = ['usuarios', 'campeonatos', 'times', 'jogos', 'boloes', 'apostas']
    const missingTables = requiredTables.filter(table => !existingTables.includes(table))

    if (missingTables.length > 0) {
      console.log('⚠️ Tabelas faltando:', missingTables)
      
      // Criar tabela times se não existir
      if (missingTables.includes('times')) {
        console.log('🔨 Criando tabela times...')
        await connection.execute(`
          CREATE TABLE IF NOT EXISTS times (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(255) NOT NULL,
            nome_curto VARCHAR(50),
            cidade VARCHAR(100),
            estado VARCHAR(100),
            pais VARCHAR(100),
            logo_url VARCHAR(500),
            api_id VARCHAR(50),
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            image_id VARCHAR(50) COMMENT 'ID da imagem (ex: 1765 para 1765.png)',
            INDEX idx_nome (nome),
            INDEX idx_api_id (api_id)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `)
        console.log('✅ Tabela times criada')
      }

      // Criar tabela campeonatos se não existir
      if (missingTables.includes('campeonatos')) {
        console.log('🔨 Criando tabela campeonatos...')
        await connection.execute(`
          CREATE TABLE IF NOT EXISTS campeonatos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(255) NOT NULL,
            codigo VARCHAR(10),
            descricao TEXT,
            pais VARCHAR(100),
            temporada VARCHAR(50),
            temporada_atual VARCHAR(50),
            status ENUM('ativo','encerrado','pausado') DEFAULT 'ativo',
            data_inicio DATE,
            data_fim DATE,
            api_id VARCHAR(50),
            logo_url VARCHAR(500),
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_api_id (api_id)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `)
        console.log('✅ Tabela campeonatos criada')
      }

      // Inserir dados básicos
      console.log('📝 Inserindo dados básicos...')
      
      // Campeonatos básicos
      await connection.execute(`
        INSERT IGNORE INTO campeonatos (id, nome, codigo, pais, status) VALUES
        (1, 'Campeonato Brasileiro Série A', 'BSA', 'Brasil', 'ativo'),
        (2, 'Copa do Brasil', 'CB', 'Brasil', 'ativo'),
        (3, 'Libertadores', 'LIB', 'América do Sul', 'ativo'),
        (4, 'Premier League', 'PL', 'Inglaterra', 'ativo'),
        (5, 'La Liga', 'LL', 'Espanha', 'ativo')
      `)

      // Times básicos
      await connection.execute(`
        INSERT IGNORE INTO times (id, nome, nome_curto, pais) VALUES
        (1, 'Flamengo', 'FLA', 'Brasil'),
        (2, 'Palmeiras', 'PAL', 'Brasil'),
        (3, 'São Paulo', 'SAO', 'Brasil'),
        (4, 'Corinthians', 'COR', 'Brasil'),
        (5, 'Santos', 'SAN', 'Brasil'),
        (6, 'Grêmio', 'GRE', 'Brasil'),
        (7, 'Internacional', 'INT', 'Brasil'),
        (8, 'Atlético Mineiro', 'ATM', 'Brasil'),
        (9, 'Cruzeiro', 'CRU', 'Brasil'),
        (10, 'Botafogo', 'BOT', 'Brasil')
      `)

      console.log('✅ Dados básicos inseridos')
    } else {
      console.log('✅ Todas as tabelas necessárias estão presentes')
    }

    // Verificar contagem final
    const [counts] = await connection.execute(`
      SELECT 
        (SELECT COUNT(*) FROM campeonatos) as total_campeonatos,
        (SELECT COUNT(*) FROM times) as total_times
    `)

    console.log('📊 Estatísticas finais:')
    console.log(`   - Campeonatos: ${counts[0].total_campeonatos}`)
    console.log(`   - Times: ${counts[0].total_times}`)

    console.log('🎉 Correção do banco de dados concluída com sucesso!')

  } catch (error) {
    console.error('❌ Erro durante a correção:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Conexão fechada')
    }
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  checkAndFixDatabase()
    .then(() => {
      console.log('✅ Script executado com sucesso')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Erro na execução:', error)
      process.exit(1)
    })
}

export { checkAndFixDatabase }
