-- =====================================================
-- SCRIPT DE EXPORTAÇÃO DO BANCO DE DADOS
-- Banco: sistema-bolao-top
-- Data: 29/07/2025, 09:12:31
-- Total de tabelas: 25
-- =====================================================

-- Criar banco de dados se não existir
CREATE DATABASE IF NOT EXISTS `sistema-bolao-top` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `sistema-bolao-top`;

-- Desabilitar verificações temporariamente
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- =====================================================
-- Tabela: afiliados
-- =====================================================

DROP TABLE IF EXISTS `afiliados`;
CREATE TABLE `afiliados` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `codigo_afiliado` varchar(20) NOT NULL,
  `percentual_comissao` decimal(5,2) DEFAULT 5.00,
  `comissao_total` decimal(10,2) DEFAULT 0.00,
  `total_indicacoes` int(11) DEFAULT 0,
  `senha_hash` varchar(255) NOT NULL,
  `status` enum('ativo','inativo','bloqueado') DEFAULT 'ativo',
  `usuario_id` int(11) DEFAULT NULL,
  `data_cadastro` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_atualizacao` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `codigo_afiliado` (`codigo_afiliado`),
  KEY `idx_email` (`email`),
  KEY `idx_codigo_afiliado` (`codigo_afiliado`),
  KEY `idx_status` (`status`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `afiliados_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela afiliados:
-- id: int(11) NOT NULL auto_increment (PRI)
-- nome: varchar(255) NOT NULL
-- email: varchar(255) NOT NULL (UNI)
-- telefone: varchar(20) NULL DEFAULT NULL
-- codigo_afiliado: varchar(20) NOT NULL (UNI)
-- percentual_comissao: decimal(5,2) NULL DEFAULT 5.00
-- comissao_total: decimal(10,2) NULL DEFAULT 0.00
-- total_indicacoes: int(11) NULL DEFAULT 0
-- senha_hash: varchar(255) NOT NULL
-- status: enum('ativo','inativo','bloqueado') NULL DEFAULT 'ativo' (MUL)
-- usuario_id: int(11) NULL DEFAULT NULL (MUL)
-- data_cadastro: timestamp NOT NULL DEFAULT current_timestamp()
-- data_atualizacao: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela afiliados:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX email (email) USING BTREE
-- UNIQUE INDEX codigo_afiliado (codigo_afiliado) USING BTREE
--  INDEX idx_email (email) USING BTREE
--  INDEX idx_codigo_afiliado (codigo_afiliado) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX usuario_id (usuario_id) USING BTREE

-- Foreign Keys da tabela afiliados:
-- afiliados_ibfk_1: usuario_id -> usuarios.id

-- Total de registros: 0

-- =====================================================
-- Tabela: afiliado_indicacoes
-- =====================================================

DROP TABLE IF EXISTS `afiliado_indicacoes`;
CREATE TABLE `afiliado_indicacoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `afiliado_id` int(11) NOT NULL,
  `usuario_indicado_id` int(11) NOT NULL,
  `valor_comissao` decimal(10,2) DEFAULT 0.00,
  `status` enum('pendente','pago','cancelado') DEFAULT 'pendente',
  `data_indicacao` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_pagamento` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_indicacao` (`afiliado_id`,`usuario_indicado_id`),
  KEY `idx_afiliado` (`afiliado_id`),
  KEY `idx_usuario_indicado` (`usuario_indicado_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `afiliado_indicacoes_ibfk_1` FOREIGN KEY (`afiliado_id`) REFERENCES `afiliados` (`id`) ON DELETE CASCADE,
  CONSTRAINT `afiliado_indicacoes_ibfk_2` FOREIGN KEY (`usuario_indicado_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela afiliado_indicacoes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- afiliado_id: int(11) NOT NULL (MUL)
-- usuario_indicado_id: int(11) NOT NULL (MUL)
-- valor_comissao: decimal(10,2) NULL DEFAULT 0.00
-- status: enum('pendente','pago','cancelado') NULL DEFAULT 'pendente' (MUL)
-- data_indicacao: timestamp NOT NULL DEFAULT current_timestamp()
-- data_pagamento: timestamp NULL DEFAULT NULL

-- Índices da tabela afiliado_indicacoes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX unique_indicacao (afiliado_id, usuario_indicado_id) USING BTREE
--  INDEX idx_afiliado (afiliado_id) USING BTREE
--  INDEX idx_usuario_indicado (usuario_indicado_id) USING BTREE
--  INDEX idx_status (status) USING BTREE

-- Foreign Keys da tabela afiliado_indicacoes:
-- afiliado_indicacoes_ibfk_1: afiliado_id -> afiliados.id
-- afiliado_indicacoes_ibfk_2: usuario_indicado_id -> usuarios.id

-- Total de registros: 0

-- =====================================================
-- Tabela: apostas
-- =====================================================

DROP TABLE IF EXISTS `apostas`;
CREATE TABLE `apostas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `bolao_id` int(11) NOT NULL,
  `valor_total` decimal(10,2) NOT NULL,
  `status` enum('pendente','paga','cancelada') DEFAULT 'pendente',
  `data_aposta` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_pagamento` timestamp NULL DEFAULT NULL,
  `codigo_bilhete` varchar(50) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `codigo_bilhete` (`codigo_bilhete`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_bolao` (`bolao_id`),
  KEY `idx_status` (`status`),
  KEY `idx_codigo_bilhete` (`codigo_bilhete`),
  KEY `idx_apostas_usuario` (`usuario_id`),
  KEY `idx_apostas_bolao` (`bolao_id`),
  KEY `idx_apostas_status` (`status`),
  CONSTRAINT `apostas_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  CONSTRAINT `apostas_ibfk_2` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela apostas:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NOT NULL (MUL)
-- bolao_id: int(11) NOT NULL (MUL)
-- valor_total: decimal(10,2) NOT NULL
-- status: enum('pendente','paga','cancelada') NULL DEFAULT 'pendente' (MUL)
-- data_aposta: timestamp NOT NULL DEFAULT current_timestamp()
-- data_pagamento: timestamp NULL DEFAULT NULL
-- codigo_bilhete: varchar(50) NULL DEFAULT NULL (UNI)
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela apostas:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX codigo_bilhete (codigo_bilhete) USING BTREE
--  INDEX idx_usuario (usuario_id) USING BTREE
--  INDEX idx_bolao (bolao_id) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_codigo_bilhete (codigo_bilhete) USING BTREE
--  INDEX idx_apostas_usuario (usuario_id) USING BTREE
--  INDEX idx_apostas_bolao (bolao_id) USING BTREE
--  INDEX idx_apostas_status (status) USING BTREE

-- Foreign Keys da tabela apostas:
-- apostas_ibfk_1: usuario_id -> usuarios.id
-- apostas_ibfk_2: bolao_id -> boloes.id

-- Total de registros: 4

-- =====================================================
-- Tabela: aposta_detalhes
-- =====================================================

DROP TABLE IF EXISTS `aposta_detalhes`;
CREATE TABLE `aposta_detalhes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aposta_id` int(11) NOT NULL,
  `jogo_id` int(11) NOT NULL,
  `resultado_apostado` enum('casa','empate','fora') NOT NULL,
  `acertou` tinyint(1) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_aposta_jogo` (`aposta_id`,`jogo_id`),
  KEY `idx_aposta` (`aposta_id`),
  KEY `idx_jogo` (`jogo_id`),
  CONSTRAINT `aposta_detalhes_ibfk_1` FOREIGN KEY (`aposta_id`) REFERENCES `apostas` (`id`) ON DELETE CASCADE,
  CONSTRAINT `aposta_detalhes_ibfk_2` FOREIGN KEY (`jogo_id`) REFERENCES `jogos` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela aposta_detalhes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- aposta_id: int(11) NOT NULL (MUL)
-- jogo_id: int(11) NOT NULL (MUL)
-- resultado_apostado: enum('casa','empate','fora') NOT NULL
-- acertou: tinyint(1) NULL DEFAULT NULL
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela aposta_detalhes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX unique_aposta_jogo (aposta_id, jogo_id) USING BTREE
--  INDEX idx_aposta (aposta_id) USING BTREE
--  INDEX idx_jogo (jogo_id) USING BTREE

-- Foreign Keys da tabela aposta_detalhes:
-- aposta_detalhes_ibfk_1: aposta_id -> apostas.id
-- aposta_detalhes_ibfk_2: jogo_id -> jogos.id

-- Total de registros: 44

-- =====================================================
-- Tabela: banners
-- =====================================================

DROP TABLE IF EXISTS `banners`;
CREATE TABLE `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `titulo` varchar(255) NOT NULL,
  `subtitulo` varchar(255) DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `cor_fundo` varchar(50) DEFAULT 'from-green-600 to-blue-600',
  `imagem_url` varchar(500) DEFAULT NULL,
  `premio_total` decimal(10,2) NOT NULL,
  `premio_primeiro` decimal(10,2) NOT NULL,
  `premio_segundo` decimal(10,2) NOT NULL,
  `valor_aposta` decimal(10,2) NOT NULL,
  `bolao_id` int(11) DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `ordem` int(11) DEFAULT 0,
  `data_inicio` datetime DEFAULT NULL,
  `data_fim` datetime DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_atualizacao` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_ativo` (`ativo`),
  KEY `idx_ordem` (`ordem`),
  KEY `idx_bolao_id` (`bolao_id`),
  CONSTRAINT `banners_ibfk_1` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela banners:
-- id: int(11) NOT NULL auto_increment (PRI)
-- titulo: varchar(255) NOT NULL
-- subtitulo: varchar(255) NULL DEFAULT NULL
-- descricao: text NULL DEFAULT NULL
-- cor_fundo: varchar(50) NULL DEFAULT 'from-green-600 to-blue-600'
-- imagem_url: varchar(500) NULL DEFAULT NULL
-- premio_total: decimal(10,2) NOT NULL
-- premio_primeiro: decimal(10,2) NOT NULL
-- premio_segundo: decimal(10,2) NOT NULL
-- valor_aposta: decimal(10,2) NOT NULL
-- bolao_id: int(11) NULL DEFAULT NULL (MUL)
-- ativo: tinyint(1) NULL DEFAULT 1 (MUL)
-- ordem: int(11) NULL DEFAULT 0 (MUL)
-- data_inicio: datetime NULL DEFAULT NULL
-- data_fim: datetime NULL DEFAULT NULL
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()
-- data_atualizacao: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela banners:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_ativo (ativo) USING BTREE
--  INDEX idx_ordem (ordem) USING BTREE
--  INDEX idx_bolao_id (bolao_id) USING BTREE

-- Foreign Keys da tabela banners:
-- banners_ibfk_1: bolao_id -> boloes.id

-- Total de registros: 0

-- =====================================================
-- Tabela: bilhetes
-- =====================================================

DROP TABLE IF EXISTS `bilhetes`;
CREATE TABLE `bilhetes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `codigo` varchar(50) NOT NULL,
  `usuario_id` int(11) DEFAULT NULL,
  `cambista_id` int(11) DEFAULT NULL,
  `usuario_nome` varchar(255) NOT NULL,
  `usuario_email` varchar(255) NOT NULL,
  `usuario_cpf` varchar(14) NOT NULL,
  `valor_total` decimal(10,2) NOT NULL,
  `quantidade_apostas` int(11) NOT NULL DEFAULT 0,
  `status` enum('pendente','pago','cancelado','expirado') DEFAULT 'pendente',
  `pago_com_saldo` tinyint(1) DEFAULT 0,
  `qr_code_pix` text DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `pix_order_id` varchar(100) DEFAULT NULL,
  `end_to_end_id` varchar(255) DEFAULT NULL,
  `data_expiracao` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `idx_codigo` (`codigo`),
  KEY `idx_usuario_email` (`usuario_email`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_cambista_id` (`cambista_id`),
  KEY `idx_pix_order_id` (`pix_order_id`),
  CONSTRAINT `bilhetes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL,
  CONSTRAINT `bilhetes_ibfk_2` FOREIGN KEY (`cambista_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela bilhetes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- codigo: varchar(50) NOT NULL (MUL)
-- usuario_id: int(11) NULL DEFAULT NULL (MUL)
-- cambista_id: int(11) NULL DEFAULT NULL (MUL)
-- usuario_nome: varchar(255) NOT NULL
-- usuario_email: varchar(255) NOT NULL (MUL)
-- usuario_cpf: varchar(14) NOT NULL
-- valor_total: decimal(10,2) NOT NULL
-- quantidade_apostas: int(11) NOT NULL DEFAULT 0
-- status: enum('pendente','pago','cancelado','expirado') NULL DEFAULT 'pendente' (MUL)
-- pago_com_saldo: tinyint(1) NULL DEFAULT 0
-- qr_code_pix: text NULL DEFAULT NULL
-- transaction_id: varchar(100) NULL DEFAULT NULL (MUL)
-- pix_order_id: varchar(100) NULL DEFAULT NULL (MUL)
-- end_to_end_id: varchar(255) NULL DEFAULT NULL
-- data_expiracao: datetime NULL DEFAULT NULL
-- created_at: timestamp NOT NULL DEFAULT current_timestamp() (MUL)
-- updated_at: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela bilhetes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX usuario_id (usuario_id) USING BTREE
--  INDEX idx_codigo (codigo) USING BTREE
--  INDEX idx_usuario_email (usuario_email) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_created_at (created_at) USING BTREE
--  INDEX idx_transaction_id (transaction_id) USING BTREE
--  INDEX idx_cambista_id (cambista_id) USING BTREE
--  INDEX idx_pix_order_id (pix_order_id) USING BTREE

-- Foreign Keys da tabela bilhetes:
-- bilhetes_ibfk_1: usuario_id -> usuarios.id
-- bilhetes_ibfk_2: cambista_id -> usuarios.id

-- Total de registros: 0

-- =====================================================
-- Tabela: bilhete_apostas
-- =====================================================

DROP TABLE IF EXISTS `bilhete_apostas`;
CREATE TABLE `bilhete_apostas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bilhete_id` int(11) NOT NULL,
  `match_id` int(11) NOT NULL,
  `resultado` enum('casa','empate','fora') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_bilhete_id` (`bilhete_id`),
  KEY `idx_match_id` (`match_id`),
  CONSTRAINT `bilhete_apostas_ibfk_1` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela bilhete_apostas:
-- id: int(11) NOT NULL auto_increment (PRI)
-- bilhete_id: int(11) NOT NULL (MUL)
-- match_id: int(11) NOT NULL (MUL)
-- resultado: enum('casa','empate','fora') NOT NULL
-- created_at: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela bilhete_apostas:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_bilhete_id (bilhete_id) USING BTREE
--  INDEX idx_match_id (match_id) USING BTREE

-- Foreign Keys da tabela bilhete_apostas:
-- bilhete_apostas_ibfk_1: bilhete_id -> bilhetes.id

-- Total de registros: 0

-- =====================================================
-- Tabela: bolao_jogos
-- =====================================================

DROP TABLE IF EXISTS `bolao_jogos`;
CREATE TABLE `bolao_jogos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bolao_id` int(11) NOT NULL,
  `jogo_id` int(11) NOT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_bolao_jogo` (`bolao_id`,`jogo_id`),
  KEY `idx_bolao` (`bolao_id`),
  KEY `idx_jogo` (`jogo_id`),
  CONSTRAINT `bolao_jogos_ibfk_1` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bolao_jogos_ibfk_2` FOREIGN KEY (`jogo_id`) REFERENCES `jogos` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=135 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela bolao_jogos:
-- id: int(11) NOT NULL auto_increment (PRI)
-- bolao_id: int(11) NOT NULL (MUL)
-- jogo_id: int(11) NOT NULL (MUL)
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela bolao_jogos:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX unique_bolao_jogo (bolao_id, jogo_id) USING BTREE
--  INDEX idx_bolao (bolao_id) USING BTREE
--  INDEX idx_jogo (jogo_id) USING BTREE

-- Foreign Keys da tabela bolao_jogos:
-- bolao_jogos_ibfk_1: bolao_id -> boloes.id
-- bolao_jogos_ibfk_2: jogo_id -> jogos.id

-- Total de registros: 0

-- =====================================================
-- Tabela: bolao_participacoes
-- =====================================================

DROP TABLE IF EXISTS `bolao_participacoes`;
CREATE TABLE `bolao_participacoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bolao_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `bilhete_id` int(11) DEFAULT NULL,
  `pontuacao` int(11) DEFAULT 0,
  `posicao` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_participation` (`bolao_id`,`usuario_id`),
  KEY `bilhete_id` (`bilhete_id`),
  KEY `idx_bolao_id` (`bolao_id`),
  KEY `idx_usuario_id` (`usuario_id`),
  KEY `idx_pontuacao` (`pontuacao`),
  CONSTRAINT `bolao_participacoes_ibfk_1` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bolao_participacoes_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bolao_participacoes_ibfk_3` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela bolao_participacoes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- bolao_id: int(11) NOT NULL (MUL)
-- usuario_id: int(11) NOT NULL (MUL)
-- bilhete_id: int(11) NULL DEFAULT NULL (MUL)
-- pontuacao: int(11) NULL DEFAULT 0 (MUL)
-- posicao: int(11) NULL DEFAULT NULL
-- created_at: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela bolao_participacoes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX unique_participation (bolao_id, usuario_id) USING BTREE
--  INDEX bilhete_id (bilhete_id) USING BTREE
--  INDEX idx_bolao_id (bolao_id) USING BTREE
--  INDEX idx_usuario_id (usuario_id) USING BTREE
--  INDEX idx_pontuacao (pontuacao) USING BTREE

-- Foreign Keys da tabela bolao_participacoes:
-- bolao_participacoes_ibfk_1: bolao_id -> boloes.id
-- bolao_participacoes_ibfk_2: usuario_id -> usuarios.id
-- bolao_participacoes_ibfk_3: bilhete_id -> bilhetes.id

-- Total de registros: 0

-- =====================================================
-- Tabela: boloes
-- =====================================================

DROP TABLE IF EXISTS `boloes`;
CREATE TABLE `boloes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `valor_aposta` decimal(10,2) NOT NULL,
  `valor_premium` decimal(10,2) DEFAULT 0.00,
  `premio_total` decimal(10,2) NOT NULL,
  `premiacao_11_pontos` decimal(10,2) DEFAULT 500.00,
  `premiacao_maior_pontuador` decimal(10,2) DEFAULT 10.00,
  `max_participantes` int(11) DEFAULT NULL,
  `min_acertos` int(11) DEFAULT 3,
  `data_inicio` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `data_fim` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('ativo','encerrado','em_breve') DEFAULT 'em_breve',
  `criado_por` int(11) NOT NULL,
  `regras` text DEFAULT NULL,
  `campeonatos_selecionados` longtext DEFAULT NULL CHECK (json_valid(`campeonatos_selecionados`)),
  `partidas_selecionadas` longtext DEFAULT NULL CHECK (json_valid(`partidas_selecionadas`)),
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  `premio_primeiro` decimal(10,2) DEFAULT 0.00,
  `premio_segundo` decimal(10,2) DEFAULT 0.00,
  `banner_image` varchar(255) DEFAULT NULL COMMENT 'URL da imagem do banner do bolão',
  PRIMARY KEY (`id`),
  KEY `criado_por` (`criado_por`),
  KEY `idx_status` (`status`),
  KEY `idx_data_inicio` (`data_inicio`),
  KEY `idx_data_fim` (`data_fim`),
  CONSTRAINT `boloes_ibfk_1` FOREIGN KEY (`criado_por`) REFERENCES `usuarios` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela boloes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- nome: varchar(255) NOT NULL
-- descricao: text NULL DEFAULT NULL
-- valor_aposta: decimal(10,2) NOT NULL
-- valor_premium: decimal(10,2) NULL DEFAULT 0.00
-- premio_total: decimal(10,2) NOT NULL
-- premiacao_11_pontos: decimal(10,2) NULL DEFAULT 500.00
-- premiacao_maior_pontuador: decimal(10,2) NULL DEFAULT 10.00
-- max_participantes: int(11) NULL DEFAULT NULL
-- min_acertos: int(11) NULL DEFAULT 3
-- data_inicio: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp() (MUL)
-- data_fim: timestamp NOT NULL DEFAULT current_timestamp() (MUL)
-- status: enum('ativo','encerrado','em_breve') NULL DEFAULT 'em_breve' (MUL)
-- criado_por: int(11) NOT NULL (MUL)
-- regras: text NULL DEFAULT NULL
-- campeonatos_selecionados: longtext NULL DEFAULT NULL
-- partidas_selecionadas: longtext NULL DEFAULT NULL
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()
-- premio_primeiro: decimal(10,2) NULL DEFAULT 0.00
-- premio_segundo: decimal(10,2) NULL DEFAULT 0.00
-- banner_image: varchar(255) NULL DEFAULT NULL

-- Índices da tabela boloes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX criado_por (criado_por) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_data_inicio (data_inicio) USING BTREE
--  INDEX idx_data_fim (data_fim) USING BTREE

-- Foreign Keys da tabela boloes:
-- boloes_ibfk_1: criado_por -> usuarios.id

-- Total de registros: 2

-- =====================================================
-- Tabela: campeonatos
-- =====================================================

DROP TABLE IF EXISTS `campeonatos`;
CREATE TABLE `campeonatos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `codigo` varchar(10) DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `temporada` varchar(50) DEFAULT NULL,
  `temporada_atual` varchar(50) DEFAULT NULL,
  `status` enum('ativo','encerrado','pausado') DEFAULT 'ativo',
  `data_inicio` date DEFAULT NULL,
  `data_fim` date DEFAULT NULL,
  `api_id` varchar(50) DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_campeonatos_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela campeonatos:
-- id: int(11) NOT NULL auto_increment (PRI)
-- nome: varchar(255) NOT NULL
-- codigo: varchar(10) NULL DEFAULT NULL
-- descricao: text NULL DEFAULT NULL
-- pais: varchar(100) NULL DEFAULT NULL
-- temporada: varchar(50) NULL DEFAULT NULL
-- temporada_atual: varchar(50) NULL DEFAULT NULL
-- status: enum('ativo','encerrado','pausado') NULL DEFAULT 'ativo' (MUL)
-- data_inicio: date NULL DEFAULT NULL
-- data_fim: date NULL DEFAULT NULL
-- api_id: varchar(50) NULL DEFAULT NULL (MUL)
-- logo_url: varchar(500) NULL DEFAULT NULL
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela campeonatos:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_api_id (api_id) USING BTREE
--  INDEX idx_campeonatos_status (status) USING BTREE

-- Total de registros: 28

-- =====================================================
-- Tabela: configuracoes
-- =====================================================

DROP TABLE IF EXISTS `configuracoes`;
CREATE TABLE `configuracoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chave` varchar(100) NOT NULL,
  `valor` text DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `tipo` enum('string','number','boolean','json') DEFAULT 'string',
  `data_atualizacao` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `chave` (`chave`),
  KEY `idx_chave` (`chave`)
) ENGINE=InnoDB AUTO_INCREMENT=145 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela configuracoes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- chave: varchar(100) NOT NULL (UNI)
-- valor: text NULL DEFAULT NULL
-- descricao: text NULL DEFAULT NULL
-- tipo: enum('string','number','boolean','json') NULL DEFAULT 'string'
-- data_atualizacao: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela configuracoes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX chave (chave) USING BTREE
--  INDEX idx_chave (chave) USING BTREE

-- Total de registros: 8

-- =====================================================
-- Tabela: depositos_pix
-- =====================================================

DROP TABLE IF EXISTS `depositos_pix`;
CREATE TABLE `depositos_pix` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `transaction_id` varchar(255) NOT NULL,
  `pix_order_id` varchar(255) DEFAULT NULL,
  `qr_code_value` text DEFAULT NULL,
  `qrcode_image` longtext DEFAULT NULL,
  `status` enum('pendente','pago','expirado','cancelado') DEFAULT 'pendente',
  `expiration_datetime` datetime DEFAULT NULL,
  `client_name` varchar(255) NOT NULL,
  `client_email` varchar(255) NOT NULL,
  `client_document` varchar(20) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `idx_usuario_id` (`usuario_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `depositos_pix_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=74 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela depositos_pix:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NOT NULL (MUL)
-- valor: decimal(10,2) NOT NULL
-- transaction_id: varchar(255) NOT NULL (UNI)
-- pix_order_id: varchar(255) NULL DEFAULT NULL
-- qr_code_value: text NULL DEFAULT NULL
-- qrcode_image: longtext NULL DEFAULT NULL
-- status: enum('pendente','pago','expirado','cancelado') NULL DEFAULT 'pendente' (MUL)
-- expiration_datetime: datetime NULL DEFAULT NULL
-- client_name: varchar(255) NOT NULL
-- client_email: varchar(255) NOT NULL
-- client_document: varchar(20) NOT NULL
-- created_at: timestamp NOT NULL DEFAULT current_timestamp() (MUL)
-- updated_at: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela depositos_pix:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX transaction_id (transaction_id) USING BTREE
--  INDEX idx_usuario_id (usuario_id) USING BTREE
--  INDEX idx_transaction_id (transaction_id) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_created_at (created_at) USING BTREE

-- Foreign Keys da tabela depositos_pix:
-- depositos_pix_ibfk_1: usuario_id -> usuarios.id

-- Total de registros: 6

-- =====================================================
-- Tabela: jogos
-- =====================================================

DROP TABLE IF EXISTS `jogos`;
CREATE TABLE `jogos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campeonato_id` int(11) NOT NULL,
  `time_casa_id` int(11) NOT NULL,
  `time_fora_id` int(11) NOT NULL,
  `data_jogo` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `local_jogo` varchar(255) DEFAULT NULL,
  `rodada` int(11) DEFAULT NULL,
  `resultado_casa` int(11) DEFAULT NULL,
  `resultado_fora` int(11) DEFAULT NULL,
  `status` enum('agendado','ao_vivo','finalizado','cancelado') DEFAULT 'agendado',
  `api_id` varchar(50) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `time_casa_id` (`time_casa_id`),
  KEY `time_fora_id` (`time_fora_id`),
  KEY `idx_data_jogo` (`data_jogo`),
  KEY `idx_status` (`status`),
  KEY `idx_campeonato` (`campeonato_id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_jogos_data` (`data_jogo`),
  KEY `idx_jogos_status` (`status`),
  KEY `idx_jogos_campeonato` (`campeonato_id`),
  CONSTRAINT `jogos_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE CASCADE,
  CONSTRAINT `jogos_ibfk_2` FOREIGN KEY (`time_casa_id`) REFERENCES `times` (`id`),
  CONSTRAINT `jogos_ibfk_3` FOREIGN KEY (`time_fora_id`) REFERENCES `times` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=537736 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela jogos:
-- id: int(11) NOT NULL auto_increment (PRI)
-- campeonato_id: int(11) NOT NULL (MUL)
-- time_casa_id: int(11) NOT NULL (MUL)
-- time_fora_id: int(11) NOT NULL (MUL)
-- data_jogo: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp() (MUL)
-- local_jogo: varchar(255) NULL DEFAULT NULL
-- rodada: int(11) NULL DEFAULT NULL
-- resultado_casa: int(11) NULL DEFAULT NULL
-- resultado_fora: int(11) NULL DEFAULT NULL
-- status: enum('agendado','ao_vivo','finalizado','cancelado') NULL DEFAULT 'agendado' (MUL)
-- api_id: varchar(50) NULL DEFAULT NULL (MUL)
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela jogos:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX time_casa_id (time_casa_id) USING BTREE
--  INDEX time_fora_id (time_fora_id) USING BTREE
--  INDEX idx_data_jogo (data_jogo) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_campeonato (campeonato_id) USING BTREE
--  INDEX idx_api_id (api_id) USING BTREE
--  INDEX idx_jogos_data (data_jogo) USING BTREE
--  INDEX idx_jogos_status (status) USING BTREE
--  INDEX idx_jogos_campeonato (campeonato_id) USING BTREE

-- Foreign Keys da tabela jogos:
-- jogos_ibfk_1: campeonato_id -> campeonatos.id
-- jogos_ibfk_2: time_casa_id -> times.id
-- jogos_ibfk_3: time_fora_id -> times.id

-- Total de registros: 935

-- =====================================================
-- Tabela: logs
-- =====================================================

DROP TABLE IF EXISTS `logs`;
CREATE TABLE `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) DEFAULT NULL,
  `acao` varchar(255) NOT NULL,
  `tabela_afetada` varchar(100) DEFAULT NULL,
  `registro_id` int(11) DEFAULT NULL,
  `dados_anteriores` longtext DEFAULT NULL CHECK (json_valid(`dados_anteriores`)),
  `dados_novos` longtext DEFAULT NULL CHECK (json_valid(`dados_novos`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `data_log` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_data_log` (`data_log`),
  KEY `idx_acao` (`acao`),
  CONSTRAINT `logs_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela logs:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NULL DEFAULT NULL (MUL)
-- acao: varchar(255) NOT NULL (MUL)
-- tabela_afetada: varchar(100) NULL DEFAULT NULL
-- registro_id: int(11) NULL DEFAULT NULL
-- dados_anteriores: longtext NULL DEFAULT NULL
-- dados_novos: longtext NULL DEFAULT NULL
-- ip_address: varchar(45) NULL DEFAULT NULL
-- user_agent: text NULL DEFAULT NULL
-- data_log: timestamp NOT NULL DEFAULT current_timestamp() (MUL)

-- Índices da tabela logs:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_usuario (usuario_id) USING BTREE
--  INDEX idx_data_log (data_log) USING BTREE
--  INDEX idx_acao (acao) USING BTREE

-- Foreign Keys da tabela logs:
-- logs_ibfk_1: usuario_id -> usuarios.id

-- Total de registros: 0

-- =====================================================
-- Tabela: pagamentos
-- =====================================================

DROP TABLE IF EXISTS `pagamentos`;
CREATE TABLE `pagamentos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aposta_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `metodo_pagamento` enum('pix','cartao','dinheiro') NOT NULL,
  `status` enum('pendente','aprovado','rejeitado','cancelado') DEFAULT 'pendente',
  `chave_pix` varchar(255) DEFAULT NULL,
  `codigo_transacao` varchar(255) DEFAULT NULL,
  `qr_code` text DEFAULT NULL,
  `txid` varchar(255) DEFAULT NULL,
  `webhook_data` longtext DEFAULT NULL CHECK (json_valid(`webhook_data`)),
  `data_pagamento` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_confirmacao` timestamp NULL DEFAULT NULL,
  `data_vencimento` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `aposta_id` (`aposta_id`),
  KEY `idx_status` (`status`),
  KEY `idx_txid` (`txid`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_pagamentos_usuario` (`usuario_id`),
  KEY `idx_pagamentos_status` (`status`),
  CONSTRAINT `pagamentos_ibfk_1` FOREIGN KEY (`aposta_id`) REFERENCES `apostas` (`id`),
  CONSTRAINT `pagamentos_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela pagamentos:
-- id: int(11) NOT NULL auto_increment (PRI)
-- aposta_id: int(11) NOT NULL (MUL)
-- usuario_id: int(11) NOT NULL (MUL)
-- valor: decimal(10,2) NOT NULL
-- metodo_pagamento: enum('pix','cartao','dinheiro') NOT NULL
-- status: enum('pendente','aprovado','rejeitado','cancelado') NULL DEFAULT 'pendente' (MUL)
-- chave_pix: varchar(255) NULL DEFAULT NULL
-- codigo_transacao: varchar(255) NULL DEFAULT NULL
-- qr_code: text NULL DEFAULT NULL
-- txid: varchar(255) NULL DEFAULT NULL (MUL)
-- webhook_data: longtext NULL DEFAULT NULL
-- data_pagamento: timestamp NOT NULL DEFAULT current_timestamp()
-- data_confirmacao: timestamp NULL DEFAULT NULL
-- data_vencimento: timestamp NULL DEFAULT NULL

-- Índices da tabela pagamentos:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX aposta_id (aposta_id) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_txid (txid) USING BTREE
--  INDEX idx_usuario (usuario_id) USING BTREE
--  INDEX idx_pagamentos_usuario (usuario_id) USING BTREE
--  INDEX idx_pagamentos_status (status) USING BTREE

-- Foreign Keys da tabela pagamentos:
-- pagamentos_ibfk_1: aposta_id -> apostas.id
-- pagamentos_ibfk_2: usuario_id -> usuarios.id

-- Total de registros: 0

-- =====================================================
-- Tabela: premios
-- =====================================================

DROP TABLE IF EXISTS `premios`;
CREATE TABLE `premios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bolao_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `aposta_id` int(11) NOT NULL,
  `acertos` int(11) NOT NULL,
  `valor_premio` decimal(10,2) NOT NULL,
  `status` enum('pendente','pago','cancelado') DEFAULT 'pendente',
  `data_premio` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_pagamento` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `aposta_id` (`aposta_id`),
  KEY `idx_bolao` (`bolao_id`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `premios_ibfk_1` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`),
  CONSTRAINT `premios_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  CONSTRAINT `premios_ibfk_3` FOREIGN KEY (`aposta_id`) REFERENCES `apostas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela premios:
-- id: int(11) NOT NULL auto_increment (PRI)
-- bolao_id: int(11) NOT NULL (MUL)
-- usuario_id: int(11) NOT NULL (MUL)
-- aposta_id: int(11) NOT NULL (MUL)
-- acertos: int(11) NOT NULL
-- valor_premio: decimal(10,2) NOT NULL
-- status: enum('pendente','pago','cancelado') NULL DEFAULT 'pendente' (MUL)
-- data_premio: timestamp NOT NULL DEFAULT current_timestamp()
-- data_pagamento: timestamp NULL DEFAULT NULL

-- Índices da tabela premios:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX aposta_id (aposta_id) USING BTREE
--  INDEX idx_bolao (bolao_id) USING BTREE
--  INDEX idx_usuario (usuario_id) USING BTREE
--  INDEX idx_status (status) USING BTREE

-- Foreign Keys da tabela premios:
-- premios_ibfk_1: bolao_id -> boloes.id
-- premios_ibfk_2: usuario_id -> usuarios.id
-- premios_ibfk_3: aposta_id -> apostas.id

-- Total de registros: 0

-- =====================================================
-- Tabela: saldo_historico
-- =====================================================

DROP TABLE IF EXISTS `saldo_historico`;
CREATE TABLE `saldo_historico` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `tipo` enum('deposito','saque','aposta','premio','bonus','estorno') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `saldo_anterior` decimal(10,2) NOT NULL,
  `saldo_novo` decimal(10,2) NOT NULL,
  `descricao` text DEFAULT NULL,
  `referencia_id` varchar(100) DEFAULT NULL,
  `referencia_tipo` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_usuario_id` (`usuario_id`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_referencia` (`referencia_id`,`referencia_tipo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela saldo_historico:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NOT NULL (MUL)
-- tipo: enum('deposito','saque','aposta','premio','bonus','estorno') NOT NULL (MUL)
-- valor: decimal(10,2) NOT NULL
-- saldo_anterior: decimal(10,2) NOT NULL
-- saldo_novo: decimal(10,2) NOT NULL
-- descricao: text NULL DEFAULT NULL
-- referencia_id: varchar(100) NULL DEFAULT NULL (MUL)
-- referencia_tipo: varchar(50) NULL DEFAULT NULL
-- created_at: timestamp NOT NULL DEFAULT current_timestamp() (MUL)

-- Índices da tabela saldo_historico:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_usuario_id (usuario_id) USING BTREE
--  INDEX idx_tipo (tipo) USING BTREE
--  INDEX idx_created_at (created_at) USING BTREE
--  INDEX idx_referencia (referencia_id, referencia_tipo) USING BTREE

-- Total de registros: 0

-- =====================================================
-- Tabela: saldo_transacoes
-- =====================================================

DROP TABLE IF EXISTS `saldo_transacoes`;
CREATE TABLE `saldo_transacoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `tipo` enum('deposito','compra_bilhete','premio','estorno','bonus') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `saldo_anterior` decimal(10,2) NOT NULL,
  `saldo_posterior` decimal(10,2) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `bilhete_id` int(11) DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `status` enum('pendente','confirmado','cancelado') DEFAULT 'confirmado',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `bilhete_id` (`bilhete_id`),
  KEY `idx_usuario_id` (`usuario_id`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_transaction_id` (`transaction_id`),
  CONSTRAINT `saldo_transacoes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `saldo_transacoes_ibfk_2` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela saldo_transacoes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NOT NULL (MUL)
-- tipo: enum('deposito','compra_bilhete','premio','estorno','bonus') NOT NULL (MUL)
-- valor: decimal(10,2) NOT NULL
-- saldo_anterior: decimal(10,2) NOT NULL
-- saldo_posterior: decimal(10,2) NOT NULL
-- descricao: varchar(255) NOT NULL
-- bilhete_id: int(11) NULL DEFAULT NULL (MUL)
-- transaction_id: varchar(255) NULL DEFAULT NULL (MUL)
-- status: enum('pendente','confirmado','cancelado') NULL DEFAULT 'confirmado' (MUL)
-- created_at: timestamp NOT NULL DEFAULT current_timestamp() (MUL)
-- updated_at: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela saldo_transacoes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX bilhete_id (bilhete_id) USING BTREE
--  INDEX idx_usuario_id (usuario_id) USING BTREE
--  INDEX idx_tipo (tipo) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_created_at (created_at) USING BTREE
--  INDEX idx_transaction_id (transaction_id) USING BTREE

-- Foreign Keys da tabela saldo_transacoes:
-- saldo_transacoes_ibfk_1: usuario_id -> usuarios.id
-- saldo_transacoes_ibfk_2: bilhete_id -> bilhetes.id

-- Total de registros: 0

-- =====================================================
-- Tabela: saques
-- =====================================================

DROP TABLE IF EXISTS `saques`;
CREATE TABLE `saques` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `chave_pix` varchar(255) NOT NULL,
  `tipo_chave` enum('cpf','cnpj','email','telefone','aleatoria') NOT NULL,
  `banco` varchar(255) DEFAULT NULL,
  `agencia` varchar(10) DEFAULT NULL,
  `conta` varchar(20) DEFAULT NULL,
  `tipo_conta` enum('corrente','poupanca') DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `status` enum('pendente','processando','aprovado','rejeitado','cancelado') DEFAULT 'pendente',
  `motivo_rejeicao` text DEFAULT NULL,
  `taxa` decimal(10,2) DEFAULT 0.00,
  `valor_liquido` decimal(10,2) NOT NULL,
  `data_processamento` timestamp NULL DEFAULT NULL,
  `data_aprovacao` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `idx_usuario_id` (`usuario_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `saques_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela saques:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NOT NULL (MUL)
-- valor: decimal(10,2) NOT NULL
-- chave_pix: varchar(255) NOT NULL
-- tipo_chave: enum('cpf','cnpj','email','telefone','aleatoria') NOT NULL
-- banco: varchar(255) NULL DEFAULT NULL
-- agencia: varchar(10) NULL DEFAULT NULL
-- conta: varchar(20) NULL DEFAULT NULL
-- tipo_conta: enum('corrente','poupanca') NULL DEFAULT NULL
-- transaction_id: varchar(255) NULL DEFAULT NULL (UNI)
-- status: enum('pendente','processando','aprovado','rejeitado','cancelado') NULL DEFAULT 'pendente' (MUL)
-- motivo_rejeicao: text NULL DEFAULT NULL
-- taxa: decimal(10,2) NULL DEFAULT 0.00
-- valor_liquido: decimal(10,2) NOT NULL
-- data_processamento: timestamp NULL DEFAULT NULL
-- data_aprovacao: timestamp NULL DEFAULT NULL
-- created_at: timestamp NOT NULL DEFAULT current_timestamp() (MUL)
-- updated_at: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela saques:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX transaction_id (transaction_id) USING BTREE
--  INDEX idx_usuario_id (usuario_id) USING BTREE
--  INDEX idx_transaction_id (transaction_id) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_created_at (created_at) USING BTREE

-- Foreign Keys da tabela saques:
-- saques_ibfk_1: usuario_id -> usuarios.id

-- Total de registros: 3

-- =====================================================
-- Tabela: scheduler_status
-- =====================================================

DROP TABLE IF EXISTS `scheduler_status`;
CREATE TABLE `scheduler_status` (
  `id` int(11) NOT NULL,
  `last_run` datetime DEFAULT NULL,
  `next_run` datetime DEFAULT NULL,
  `status` text DEFAULT 'idle',
  `error_count` int(11) DEFAULT 0,
  `last_error` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela scheduler_status:
-- id: int(11) NOT NULL (PRI)
-- last_run: datetime NULL DEFAULT NULL
-- next_run: datetime NULL DEFAULT NULL
-- status: text NULL DEFAULT 'idle'
-- error_count: int(11) NULL DEFAULT 0
-- last_error: text NULL DEFAULT NULL
-- created_at: datetime NULL DEFAULT current_timestamp()
-- updated_at: datetime NULL DEFAULT current_timestamp()

-- Índices da tabela scheduler_status:
-- UNIQUE INDEX PRIMARY (id) USING BTREE

-- Total de registros: 1

-- =====================================================
-- Tabela: times
-- =====================================================

DROP TABLE IF EXISTS `times`;
CREATE TABLE `times` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `nome_curto` varchar(50) DEFAULT NULL,
  `cidade` varchar(100) DEFAULT NULL,
  `estado` varchar(100) DEFAULT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `api_id` varchar(50) DEFAULT NULL,
  `data_criacao` timestamp NOT NULL DEFAULT current_timestamp(),
  `image_id` varchar(50) DEFAULT NULL COMMENT 'ID da imagem (ex: 1765 para 1765.png)',
  PRIMARY KEY (`id`),
  KEY `idx_nome` (`nome`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_times_api` (`api_id`)
) ENGINE=InnoDB AUTO_INCREMENT=581 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela times:
-- id: int(11) NOT NULL auto_increment (PRI)
-- nome: varchar(255) NOT NULL (MUL)
-- nome_curto: varchar(50) NULL DEFAULT NULL
-- cidade: varchar(100) NULL DEFAULT NULL
-- estado: varchar(100) NULL DEFAULT NULL
-- pais: varchar(100) NULL DEFAULT NULL
-- logo_url: varchar(500) NULL DEFAULT NULL
-- api_id: varchar(50) NULL DEFAULT NULL (MUL)
-- data_criacao: timestamp NOT NULL DEFAULT current_timestamp()
-- image_id: varchar(50) NULL DEFAULT NULL

-- Índices da tabela times:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_nome (nome) USING BTREE
--  INDEX idx_api_id (api_id) USING BTREE
--  INDEX idx_times_api (api_id) USING BTREE

-- Total de registros: 580

-- =====================================================
-- Tabela: transacoes
-- =====================================================

DROP TABLE IF EXISTS `transacoes`;
CREATE TABLE `transacoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `tipo` enum('deposito','compra_bilhete','premio','estorno','bonus','comissao','saque') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `saldo_anterior` decimal(10,2) NOT NULL,
  `saldo_posterior` decimal(10,2) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `bilhete_id` int(11) DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `status` enum('pendente','confirmado','cancelado') DEFAULT 'confirmado',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_usuario_id` (`usuario_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `transacoes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela transacoes:
-- id: int(11) NOT NULL auto_increment (PRI)
-- usuario_id: int(11) NOT NULL (MUL)
-- tipo: enum('deposito','compra_bilhete','premio','estorno','bonus','comissao','saque') NOT NULL
-- valor: decimal(10,2) NOT NULL
-- saldo_anterior: decimal(10,2) NOT NULL
-- saldo_posterior: decimal(10,2) NOT NULL
-- descricao: varchar(255) NOT NULL
-- bilhete_id: int(11) NULL DEFAULT NULL
-- transaction_id: varchar(255) NULL DEFAULT NULL (MUL)
-- status: enum('pendente','confirmado','cancelado') NULL DEFAULT 'confirmado' (MUL)
-- created_at: timestamp NOT NULL DEFAULT current_timestamp() (MUL)
-- updated_at: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()

-- Índices da tabela transacoes:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_usuario_id (usuario_id) USING BTREE
--  INDEX idx_transaction_id (transaction_id) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_created_at (created_at) USING BTREE

-- Foreign Keys da tabela transacoes:
-- transacoes_ibfk_1: usuario_id -> usuarios.id

-- Total de registros: 0

-- =====================================================
-- Tabela: usuarios
-- =====================================================

DROP TABLE IF EXISTS `usuarios`;
CREATE TABLE `usuarios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `usuario` varchar(255) DEFAULT NULL,
  `comissao` decimal(5,2) DEFAULT 5.00,
  `observacao` text DEFAULT NULL,
  `pode_criar_cambista` tinyint(1) DEFAULT 0,
  `pode_colocar_comissao` tinyint(1) DEFAULT 0,
  `gerente_id` int(11) DEFAULT NULL,
  `endereco` text DEFAULT NULL,
  `cpf_cnpj` varchar(20) DEFAULT NULL,
  `senha_hash` varchar(255) NOT NULL,
  `tipo` enum('admin','usuario','cambista','gerente','supervisor') DEFAULT 'usuario',
  `status` enum('ativo','inativo','bloqueado') DEFAULT 'ativo',
  `saldo` decimal(10,2) DEFAULT 0.00,
  `data_cadastro` timestamp NOT NULL DEFAULT current_timestamp(),
  `ultimo_acesso` timestamp NULL DEFAULT NULL,
  `data_atualizacao` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `afiliado_id` int(11) DEFAULT NULL,
  `porcentagem_comissao` decimal(5,2) DEFAULT 0.00,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_status` (`status`),
  KEY `idx_afiliado_id` (`afiliado_id`),
  KEY `idx_saldo` (`saldo`),
  CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`afiliado_id`) REFERENCES `afiliados` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=1003 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela usuarios:
-- id: int(11) NOT NULL auto_increment (PRI)
-- nome: varchar(255) NOT NULL
-- email: varchar(255) NOT NULL (UNI)
-- telefone: varchar(20) NULL DEFAULT NULL
-- usuario: varchar(255) NULL DEFAULT NULL
-- comissao: decimal(5,2) NULL DEFAULT 5.00
-- observacao: text NULL DEFAULT NULL
-- pode_criar_cambista: tinyint(1) NULL DEFAULT 0
-- pode_colocar_comissao: tinyint(1) NULL DEFAULT 0
-- gerente_id: int(11) NULL DEFAULT NULL
-- endereco: text NULL DEFAULT NULL
-- cpf_cnpj: varchar(20) NULL DEFAULT NULL
-- senha_hash: varchar(255) NOT NULL
-- tipo: enum('admin','usuario','cambista','gerente','supervisor') NULL DEFAULT 'usuario' (MUL)
-- status: enum('ativo','inativo','bloqueado') NULL DEFAULT 'ativo' (MUL)
-- saldo: decimal(10,2) NULL DEFAULT 0.00 (MUL)
-- data_cadastro: timestamp NOT NULL DEFAULT current_timestamp()
-- ultimo_acesso: timestamp NULL DEFAULT NULL
-- data_atualizacao: timestamp NOT NULL DEFAULT current_timestamp() on update current_timestamp()
-- afiliado_id: int(11) NULL DEFAULT NULL (MUL)
-- porcentagem_comissao: decimal(5,2) NULL DEFAULT 0.00

-- Índices da tabela usuarios:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
-- UNIQUE INDEX email (email) USING BTREE
--  INDEX idx_email (email) USING BTREE
--  INDEX idx_tipo (tipo) USING BTREE
--  INDEX idx_status (status) USING BTREE
--  INDEX idx_afiliado_id (afiliado_id) USING BTREE
--  INDEX idx_saldo (saldo) USING BTREE

-- Foreign Keys da tabela usuarios:
-- usuarios_ibfk_1: afiliado_id -> afiliados.id

-- Total de registros: 11

-- =====================================================
-- Tabela: webhook_logs
-- =====================================================

DROP TABLE IF EXISTS `webhook_logs`;
CREATE TABLE `webhook_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `order_id` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `end_to_end_id` varchar(255) DEFAULT NULL,
  `webhook_data` text DEFAULT NULL,
  `processed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Colunas da tabela webhook_logs:
-- id: int(11) NOT NULL auto_increment (PRI)
-- transaction_id: varchar(255) NULL DEFAULT NULL (MUL)
-- order_id: varchar(255) NULL DEFAULT NULL (MUL)
-- amount: decimal(10,2) NULL DEFAULT NULL
-- status: varchar(50) NULL DEFAULT NULL
-- end_to_end_id: varchar(255) NULL DEFAULT NULL
-- webhook_data: text NULL DEFAULT NULL
-- processed_at: timestamp NOT NULL DEFAULT current_timestamp()

-- Índices da tabela webhook_logs:
-- UNIQUE INDEX PRIMARY (id) USING BTREE
--  INDEX idx_transaction_id (transaction_id) USING BTREE
--  INDEX idx_order_id (order_id) USING BTREE

-- Total de registros: 0

-- =====================================================
-- FINALIZAÇÃO
-- =====================================================

-- Reabilitar verificações
SET FOREIGN_KEY_CHECKS = 1;
COMMIT;

-- Script gerado em: 29/07/2025, 09:12:31
