#!/bin/bash

echo "🔧 Corrigindo permissões do Next.js..."

# Verificar se estamos no diretório correto
if [ ! -f "package.json" ]; then
    echo "❌ Erro: package.json não encontrado. Execute este script no diretório raiz do projeto."
    exit 1
fi

echo "📦 Limpando instalação anterior..."
rm -rf node_modules package-lock.json

echo "🧹 Limpando cache do npm..."
npm cache clean --force

echo "📥 Reinstalando dependências..."
npm install

echo "🔐 Corrigindo permissões dos binários..."
chmod +x node_modules/.bin/*

echo "👤 Corrigindo proprietário dos arquivos..."
sudo chown -R $USER:$USER node_modules

echo "✅ Correções aplicadas!"
echo ""
echo "🚀 Testando o servidor de desenvolvimento..."
echo "Execute: npm run dev"
echo ""
echo "Se ainda houver problemas, tente:"
echo "1. npx next dev -p 3000"
echo "2. yarn install && yarn dev"
echo "3. sudo npm run dev (não recomendado)"
