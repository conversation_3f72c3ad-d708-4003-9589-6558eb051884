'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RefreshCw, <PERSON>, Clock, UserCheck, UserX } from 'lucide-react'

interface Sessao {
  id: number
  nome: string
  email: string
  tipo: string
  status: string
  isOnline: boolean
  sessionStart: string | null
  tempoSessao: number
  ultimoLogin: string
  dataCadastro: string
}

interface SessionMonitorProps {
  className?: string
}

export function SessionMonitor({ className = '' }: SessionMonitorProps) {
  const [sessoes, setSessoes] = useState<Sessao[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({ total: 0, online: 0, offline: 0 })

  const loadSessions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/sessions')
      if (response.ok) {
        const data = await response.json()
        setSessoes(data.sessoes || [])
        setStats({
          total: data.total || 0,
          online: data.online || 0,
          offline: data.offline || 0
        })
      }
    } catch (error) {
      console.error('Erro ao carregar sessões:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadSessions()
    
    // Atualizar a cada 30 segundos
    const interval = setInterval(loadSessions, 30000)
    return () => clearInterval(interval)
  }, [])

  const formatSessionDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'admin': return '👑'
      case 'gerente': return '👨‍💼'
      case 'cambista': return '👨‍💼'
      default: return '👤'
    }
  }

  const getTipoBadge = (tipo: string) => {
    const variants = {
      admin: 'bg-red-600 text-white',
      gerente: 'bg-blue-600 text-white',
      cambista: 'bg-green-600 text-white'
    }
    
    const labels = {
      admin: 'Administrador',
      gerente: 'Gerente',
      cambista: 'Cambista'
    }

    return (
      <Badge className={`${variants[tipo as keyof typeof variants]} text-xs`}>
        {labels[tipo as keyof typeof labels] || tipo}
      </Badge>
    )
  }

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <CardTitle>Monitoramento de Sessões</CardTitle>
          </div>
          <Button
            onClick={loadSessions}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
        
        {/* Estatísticas */}
        <div className="flex space-x-4 mt-4">
          <div className="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-lg">
            <Users className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Total: {stats.total}</span>
          </div>
          <div className="flex items-center space-x-2 px-3 py-1 bg-green-100 rounded-lg">
            <UserCheck className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">Online: {stats.online}</span>
          </div>
          <div className="flex items-center space-x-2 px-3 py-1 bg-yellow-100 rounded-lg">
            <UserX className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">Offline: {stats.offline}</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
            <p className="text-gray-500 mt-2">Carregando sessões...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sessoes.map((sessao) => (
              <div
                key={sessao.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center space-x-3">
                  {/* Status Indicator */}
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        sessao.isOnline 
                          ? 'bg-green-400 animate-pulse' 
                          : 'bg-yellow-400'
                      }`}
                    />
                    <span className="text-lg">{getTipoIcon(sessao.tipo)}</span>
                  </div>
                  
                  {/* User Info */}
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{sessao.nome}</span>
                      {getTipoBadge(sessao.tipo)}
                    </div>
                    <div className="text-sm text-gray-500">
                      ID: {sessao.id} • {sessao.email}
                    </div>
                  </div>
                </div>
                
                {/* Session Info */}
                <div className="text-right">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="font-mono text-sm">
                      {formatSessionDuration(sessao.tempoSessao)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {sessao.isOnline ? 'Online agora' : `Último: ${sessao.ultimoLogin}`}
                  </div>
                </div>
              </div>
            ))}
            
            {sessoes.length === 0 && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto" />
                <p className="text-gray-500 mt-2">Nenhuma sessão encontrada</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
