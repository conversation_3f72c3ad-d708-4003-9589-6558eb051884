// Teste rápido da API Football Data
const FOOTBALL_API_TOKEN = 'cbeb9f19b15e4252b3f9d3375fefcfcc'
const FOOTBALL_API_URL = 'https://api.football-data.org/v4'

async function testAPI() {
  try {
    console.log('🌐 Testando API Football Data...')
    
    const response = await fetch(`${FOOTBALL_API_URL}/competitions`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    console.log(`📡 Status: ${response.status}`)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.log('❌ Erro:', errorText)
      return
    }

    const data = await response.json()
    console.log('✅ API funcionando!')
    console.log(`📊 ${data.competitions?.length || 0} competições disponíveis`)
    
    // Mostrar algumas competições
    if (data.competitions) {
      console.log('\n🏆 Competições disponíveis:')
      data.competitions.slice(0, 5).forEach(comp => {
        console.log(`   - ${comp.name} (${comp.area?.name})`)
      })
    }

  } catch (error) {
    console.error('❌ Erro:', error.message)
  }
}

testAPI()
