import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const cambistaId = searchParams.get("cambista_id") // ID do cambista logado
    const filters = {
      status: searchParams.get("status") || undefined,
      search: searchParams.get("search") || undefined,
    }

    console.log(`🎯 Cambista ${cambistaId}: Buscando usuários comuns`)

    // Construir condições WHERE
    let whereConditions = ["u.tipo = 'usuario'"] // Cambista só vê usuários comuns
    let queryParams = []

    // Aplicar filtros adicionais
    if (filters.status) {
      whereConditions.push("u.status = ?")
      queryParams.push(filters.status)
    }

    if (filters.search) {
      whereConditions.push("(u.nome LIKE ? OR u.email LIKE ?)")
      queryParams.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // Buscar usuários
    const usuarios = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.tipo,
        u.status,
        u.saldo,
        u.data_cadastro,
        COALESCE(bilhetes.total_bilhetes, 0) as total_bilhetes,
        COALESCE(bilhetes.valor_total, 0) as valor_total_apostas,
        COALESCE(bilhetes.bilhetes_ativos, 0) as bilhetes_ativos
      FROM usuarios u
      LEFT JOIN (
        SELECT
          usuario_id,
          COUNT(*) as total_bilhetes,
          SUM(valor_total) as valor_total,
          SUM(CASE WHEN status = 'pago' THEN 1 ELSE 0 END) as bilhetes_ativos
        FROM bilhetes
        WHERE status != 'cancelado'
        GROUP BY usuario_id
      ) bilhetes ON u.id = bilhetes.usuario_id
      ${whereClause}
      ORDER BY u.data_cadastro DESC
      LIMIT 100
    `, queryParams)

    console.log(`📊 ${usuarios?.length || 0} usuários encontrados`)

    // Formatar usuários para o frontend
    const usuariosFormatados = (usuarios || []).map((usuario: any) => ({
      id: usuario.id,
      nome: usuario.nome,
      email: usuario.email,
      telefone: usuario.telefone || '',
      tipo: usuario.tipo,
      status: usuario.status,
      saldo: parseFloat(usuario.saldo || 0),
      data_cadastro: new Date(usuario.data_cadastro).toLocaleDateString('pt-BR'),
      total_bilhetes: parseInt(usuario.total_bilhetes || 0),
      valor_total_apostas: parseFloat(usuario.valor_total_apostas || 0),
      bilhetes_ativos: parseInt(usuario.bilhetes_ativos || 0),
      // Cambista pode editar apenas dados básicos de usuários
      pode_editar: true,
      pode_excluir: false // Cambista não pode excluir usuários
    }))

    // Calcular estatísticas
    const stats = {
      total: usuariosFormatados.length,
      ativos: usuariosFormatados.filter(u => u.status === 'ativo').length,
      inativos: usuariosFormatados.filter(u => u.status === 'inativo').length,
      com_apostas: usuariosFormatados.filter(u => u.total_bilhetes > 0).length,
      valor_total_apostas: usuariosFormatados.reduce((sum, u) => sum + u.valor_total_apostas, 0)
    }

    return NextResponse.json({
      success: true,
      usuarios: usuariosFormatados,
      stats,
      user_type: 'cambista',
      total: usuariosFormatados.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar usuários do cambista:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      usuarios: []
    }, { status: 500 })
  }
}
