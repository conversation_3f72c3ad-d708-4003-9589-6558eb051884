// Configuração centralizada para APIs externas

export const FOOTBALL_API_CONFIG = {
  // Configurações básicas
  BASE_URL: process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4',
  TOKEN: process.env.FOOTBALL_API_TOKEN,
  
  // Rate limiting (plano gratuito)
  MAX_REQUESTS_PER_MINUTE: 8, // Conservador (limite real é 10)
  REQUEST_DELAY: 8000, // 8 segundos entre requisições
  WINDOW_DURATION: 60 * 1000, // 1 minuto
  
  // Cache
  CACHE_DURATION: 30 * 60 * 1000, // 30 minutos
  
  // Timeouts
  REQUEST_TIMEOUT: 10000, // 10 segundos
  RETRY_DELAY: 60000, // 1 minuto para retry após rate limit
  
  // Competições prioritárias (buscar primeiro)
  PRIORITY_COMPETITIONS: ['PL', 'PD', 'SA', 'BL1', 'FL1'],
  
  // Competições brasileiras
  BRAZILIAN_COMPETITIONS: ['BSA', 'CB'],
  
  // Fallback para dados locais
  USE_LOCAL_FALLBACK: true,
  
  // Modo de desenvolvimento (mais conservador)
  DEVELOPMENT_MODE: process.env.NODE_ENV === 'development'
}

// Estado global do rate limiting
export const API_STATE = {
  requestCount: 0,
  windowStart: Date.now(),
  lastRequestTime: 0,
  isBlocked: false,
  blockUntil: 0
}

// Função para verificar se pode fazer requisição
export function canMakeRequest() {
  const now = Date.now()
  
  // Verificar se está bloqueado
  if (API_STATE.isBlocked && now < API_STATE.blockUntil) {
    return false
  }
  
  // Resetar bloqueio se expirou
  if (API_STATE.isBlocked && now >= API_STATE.blockUntil) {
    API_STATE.isBlocked = false
    API_STATE.requestCount = 0
    API_STATE.windowStart = now
  }
  
  // Resetar contador se passou da janela
  if (now - API_STATE.windowStart > FOOTBALL_API_CONFIG.WINDOW_DURATION) {
    API_STATE.requestCount = 0
    API_STATE.windowStart = now
  }
  
  // Verificar limite
  return API_STATE.requestCount < FOOTBALL_API_CONFIG.MAX_REQUESTS_PER_MINUTE
}

// Função para registrar requisição
export function registerRequest() {
  API_STATE.requestCount++
  API_STATE.lastRequestTime = Date.now()
}

// Função para bloquear após rate limit
export function blockAfterRateLimit() {
  API_STATE.isBlocked = true
  API_STATE.blockUntil = Date.now() + FOOTBALL_API_CONFIG.RETRY_DELAY
  API_STATE.requestCount = FOOTBALL_API_CONFIG.MAX_REQUESTS_PER_MINUTE
}

// Função para calcular delay necessário
export function getRequiredDelay() {
  const now = Date.now()
  const timeSinceLastRequest = now - API_STATE.lastRequestTime
  
  if (timeSinceLastRequest < FOOTBALL_API_CONFIG.REQUEST_DELAY) {
    return FOOTBALL_API_CONFIG.REQUEST_DELAY - timeSinceLastRequest
  }
  
  return 0
}

// Função para obter status da API
export function getAPIStatus() {
  const now = Date.now()
  
  return {
    canMakeRequest: canMakeRequest(),
    requestCount: API_STATE.requestCount,
    maxRequests: FOOTBALL_API_CONFIG.MAX_REQUESTS_PER_MINUTE,
    windowTimeLeft: Math.max(0, FOOTBALL_API_CONFIG.WINDOW_DURATION - (now - API_STATE.windowStart)),
    isBlocked: API_STATE.isBlocked,
    blockTimeLeft: Math.max(0, API_STATE.blockUntil - now),
    requiredDelay: getRequiredDelay()
  }
}

// Função para log do status
export function logAPIStatus(context = '') {
  const status = getAPIStatus()
  
  console.log(`📊 API Status ${context}:`, {
    canMakeRequest: status.canMakeRequest,
    requests: `${status.requestCount}/${status.maxRequests}`,
    windowTimeLeft: `${Math.ceil(status.windowTimeLeft/1000)}s`,
    isBlocked: status.isBlocked,
    blockTimeLeft: status.isBlocked ? `${Math.ceil(status.blockTimeLeft/1000)}s` : '0s'
  })
}
