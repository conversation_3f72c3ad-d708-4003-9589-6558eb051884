import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const gerenteId = searchParams.get("gerente_id") // ID do gerente logado
    const filters = {
      status: searchParams.get("status") || undefined,
      tipo: searchParams.get("tipo") || undefined,
      search: searchParams.get("search") || undefined,
    }

    console.log(`👨‍💼 Gerente ${gerenteId}: Buscando usuários e cambistas`)

    // Construir condições WHERE
    let whereConditions = []
    let queryParams = []

    // Gerente vê seus cambistas e usuários comuns
    if (gerenteId) {
      whereConditions.push("(u.tipo IN ('cambista', 'usuario') AND (u.gerente_id = ? OR u.gerente_id IS NULL))")
      queryParams.push(gerenteId)
    } else {
      // Se não informar gerente_id, mostrar apenas usuários comuns
      whereConditions.push("u.tipo = 'usuario'")
    }

    // Aplicar filtros adicionais
    if (filters.status) {
      whereConditions.push("u.status = ?")
      queryParams.push(filters.status)
    }

    if (filters.tipo) {
      whereConditions.push("u.tipo = ?")
      queryParams.push(filters.tipo)
    }

    if (filters.search) {
      whereConditions.push("(u.nome LIKE ? OR u.email LIKE ?)")
      queryParams.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // Buscar usuários
    const usuarios = await executeQuery(`
      SELECT
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.tipo,
        u.status,
        u.saldo,
        u.data_cadastro,
        u.gerente_id,
        u.porcentagem_comissao,
        g.nome as gerente_nome,
        COALESCE(bilhetes.total_bilhetes, 0) as total_bilhetes,
        COALESCE(bilhetes.valor_total, 0) as valor_total_apostas
      FROM usuarios u
      LEFT JOIN usuarios g ON u.gerente_id = g.id
      LEFT JOIN (
        SELECT
          usuario_id,
          COUNT(*) as total_bilhetes,
          SUM(valor_total) as valor_total
        FROM bilhetes
        WHERE status != 'cancelado'
        GROUP BY usuario_id
      ) bilhetes ON u.id = bilhetes.usuario_id
      ${whereClause}
      ORDER BY
        CASE u.tipo
          WHEN 'cambista' THEN 1
          WHEN 'usuario' THEN 2
        END,
        u.data_cadastro DESC
      LIMIT 100
    `, queryParams)

    console.log(`📊 ${usuarios?.length || 0} usuários encontrados`)

    // Formatar usuários para o frontend
    const usuariosFormatados = (usuarios || []).map((usuario: any) => ({
      id: usuario.id,
      nome: usuario.nome,
      email: usuario.email,
      telefone: usuario.telefone || '',
      tipo: usuario.tipo,
      status: usuario.status,
      saldo: parseFloat(usuario.saldo || 0),
      data_cadastro: new Date(usuario.data_cadastro).toLocaleDateString('pt-BR'),
      gerente_id: usuario.gerente_id,
      gerente_nome: usuario.gerente_nome || '',
      porcentagem_comissao: parseFloat(usuario.porcentagem_comissao || 0),
      total_bilhetes: parseInt(usuario.total_bilhetes || 0),
      valor_total_apostas: parseFloat(usuario.valor_total_apostas || 0),
      pode_editar: ['cambista', 'usuario'].includes(usuario.tipo),
      pode_excluir: ['cambista', 'usuario'].includes(usuario.tipo)
    }))

    // Calcular estatísticas
    const stats = {
      total: usuariosFormatados.length,
      ativos: usuariosFormatados.filter(u => u.status === 'ativo').length,
      cambistas: usuariosFormatados.filter(u => u.tipo === 'cambista').length,
      usuarios: usuariosFormatados.filter(u => u.tipo === 'usuario').length
    }

    return NextResponse.json({
      success: true,
      usuarios: usuariosFormatados,
      stats,
      user_type: 'gerente',
      total: usuariosFormatados.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar usuários do gerente:', error)

    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      usuarios: []
    }, { status: 500 })
  }
}
