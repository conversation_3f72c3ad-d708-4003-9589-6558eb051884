import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Configurando colunas de sessão na tabela usuarios...')

    // Verificar se as colunas já existem
    const columns = await executeQuery(`
      SHOW COLUMNS FROM usuarios LIKE 'is_online'
    `)

    if (columns.length === 0) {
      // Adicionar colunas de sessão
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN is_online BOOLEAN DEFAULT FALSE,
        ADD COLUMN session_start DATETIME NULL,
        ADD COLUMN session_duration INT DEFAULT 0,
        ADD COLUMN ultimo_login DATETIME NULL
      `)
      console.log('✅ Colunas de sessão adicionadas')
    } else {
      console.log('ℹ️ Colunas de sessão já existem')
    }

    // Atualizar usuários existentes
    await executeQuery(`
      UPDATE usuarios SET 
        is_online = FALSE,
        session_duration = 0
      WHERE is_online IS NULL
    `)

    // Criar índices para melhor performance
    try {
      await executeQuery(`
        CREATE INDEX idx_usuarios_online ON usuarios(is_online)
      `)
    } catch (e) {
      console.log('ℹ️ Índice idx_usuarios_online já existe')
    }

    try {
      await executeQuery(`
        CREATE INDEX idx_usuarios_session ON usuarios(session_start)
      `)
    } catch (e) {
      console.log('ℹ️ Índice idx_usuarios_session já existe')
    }

    try {
      await executeQuery(`
        CREATE INDEX idx_usuarios_tipo ON usuarios(tipo)
      `)
    } catch (e) {
      console.log('ℹ️ Índice idx_usuarios_tipo já existe')
    }

    console.log('✅ Configuração de sessões concluída')

    return NextResponse.json({
      success: true,
      message: 'Colunas de sessão configuradas com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao configurar sessões:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro interno do servidor',
      error: (error as Error).message
    }, { status: 500 })
  }
}
