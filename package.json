{"name": "sistema-bolao", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "npx next dev -p 3000", "build": "npx next build", "start": "npx next start -p 3000", "start:3000": "npx next start", "start:safe": "node scripts/start-server.js", "start:standalone": "node .next/standalone/server.js", "start:prod": "NODE_ENV=production npx next start -p 80", "start:prod-3000": "NODE_ENV=production npx next start -p 3000", "build:prod": "NODE_ENV=production npx next build", "lint": "npx next lint", "db:setup": "node scripts/init-mysql.js", "db:setup:sqlite": "node scripts/init-sqlite.js", "db:reset": "node scripts/init-mysql.js", "db:test": "node scripts/test-mysql-connection.js", "db:check": "node scripts/test-connection.js", "db:fix-boloes": "node scripts/fix-bolao-jogos.js", "db:fix": "node scripts/fix-database-issues.js", "api:check": "node scripts/check-football-api.js", "sync:football": "node scripts/sync-football-data.js", "sync:football:sqlite": "node scripts/sync-football-sqlite.js", "sync:run": "node scripts/run-sync.js", "sync:once": "node scripts/scheduler.js --once", "sync:scheduler": "node scripts/scheduler.js", "sync:start": "node scripts/scheduler.js", "deploy": "node scripts/deploy-production.js", "deploy:3000": "node scripts/deploy-production.js --port-3000"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@types/bcryptjs": "^2.4.6", "@types/qrcode": "^1.5.5", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "fs": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "mysql2": "^3.14.1", "next": "^14.2.16", "next-themes": "latest", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "node-gyp": "latest", "path": "latest", "qrcode": "^1.5.4", "react": "^18", "react-day-picker": "latest", "react-dom": "^18", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "latest", "sweetalert2": "^11.22.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.16.2", "@types/react": "^18.3.23", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}